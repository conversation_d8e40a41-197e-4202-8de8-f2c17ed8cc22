package co.yixiang.yshop.module.pay.service.impl;

import co.yixiang.yshop.framework.common.exception.ServiceException;
import co.yixiang.yshop.module.pay.config.UnionPayWxMiniProgramConfig;
import co.yixiang.yshop.module.pay.dto.UnionPayCodePayRequest;
import co.yixiang.yshop.module.pay.dto.UnionPayMerchantConfig;
import co.yixiang.yshop.module.pay.service.UnionPayAccessTokenService;
import co.yixiang.yshop.module.pay.service.UnionPayCodePayService;
import co.yixiang.yshop.module.pay.service.UnionPayConfigService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * UnionPay付款码支付服务实现
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnionPayCodePayServiceImpl implements UnionPayCodePayService {

    private final UnionPayConfigService unionPayConfigService;
    private final UnionPayAccessTokenService accessTokenService;
    private final UnionPayWxMiniProgramConfig config;
    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public Map<String, Object> activeTerminal(String detailsId) {
        try {
            log.info("开始激活银联终端，detailsId: {}", detailsId);

            // 1. 获取商户配置
            UnionPayMerchantConfig merchantConfig = unionPayConfigService.getUnionPayConfig(detailsId);

            // 2. 构建激活终端请求
            JSONObject params = new JSONObject();
            params.put("merchantCode", merchantConfig.getMid());
            params.put("terminalCode", merchantConfig.getTid());

            // 3. 发送激活请求
            Map<String, Object> response = sendJsonRequest(config.getActiveTerminalUrl(), params, detailsId);

            log.info("银联终端激活响应: {}", JSON.toJSONString(response));
            return response;

        } catch (Exception e) {
            log.error("银联终端激活异常: detailsId={}, error={}", detailsId, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("errCode", "ERROR");
            errorResult.put("errInfo", "终端激活异常: " + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public Map<String, Object> codePay(String orderId, Long amount, String payCode, String detailsId) {
        log.info("银联付款码支付开始: orderId={}, amount={}, payCode={}, detailsId={}", 
                orderId, amount, payCode, detailsId);
        
        try {
            // 1. 激活终端
            // Map<String, Object> activeResult = activeTerminal(detailsId);
            // if (!isActiveSuccess(activeResult)) {
            //     log.warn("终端激活失败，但继续尝试支付: {}", activeResult.get("errInfo"));
            //     // 终端激活失败不阻断支付流程，因为终端可能已经激活过了
            // } else {
            //     log.info("终端激活成功");
            // }

            // 2. 获取商户配置
            UnionPayMerchantConfig merchantConfig = unionPayConfigService.getUnionPayConfig(detailsId);

            // 3. 构建请求参数
            UnionPayCodePayRequest request = buildCodePayRequest(orderId, amount, payCode, merchantConfig);

            // 4. 发送支付请求
            Map<String, Object> response = sendCodePayRequest(request, detailsId);
            
            log.info("银联付款码支付响应: {}", JSON.toJSONString(response));
            return response;
            
        } catch (Exception e) {
            log.error("银联付款码支付异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new ServiceException(500, "银联付款码支付失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> queryOrder(String orderId, String detailsId) {
        log.info("银联订单查询开始: orderId={}, detailsId={}", orderId, detailsId);
        
        try {
            // 1. 获取商户配置
            UnionPayMerchantConfig merchantConfig = unionPayConfigService.getUnionPayConfig(detailsId);

            // 2. 构建查询请求
            JSONObject queryParams = new JSONObject();
            queryParams.put("merchantCode", merchantConfig.getMid());
            queryParams.put("terminalCode", merchantConfig.getTid());
            queryParams.put("merchantOrderId", orderId);
            
            // 4. 发送查询请求
            Map<String, Object> response = sendJsonRequest(config.getQueryUrl(), queryParams, detailsId);
            
            log.info("银联订单查询响应: {}", JSON.toJSONString(response));
            return response;
            
        } catch (Exception e) {
            log.error("银联订单查询异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new ServiceException(500, "银联订单查询失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean isPaySuccess(Map<String, Object> response) {
        if (response == null) {
            return false;
        }
        
        // 银联付款码支付成功的判断条件
        String errCode = (String) response.get("errCode");
        String errInfo = (String) response.get("errInfo");
        
        // errCode为SUCCESS且errInfo为空或成功信息表示支付成功
        return "SUCCESS".equals(errCode) && (errInfo == null || errInfo.isEmpty() || "SUCCESS".equals(errInfo));
    }

    @Override
    public boolean isActiveSuccess(Map<String, Object> response) {
        if (response == null) {
            return false;
        }

        // 银联终端激活成功的判断条件
        String errCode = (String) response.get("errCode");
        String errInfo = (String) response.get("errInfo");

        // errCode为SUCCESS且errInfo为空或成功信息表示激活成功
        return "SUCCESS".equals(errCode) && (errInfo == null || errInfo.isEmpty() || "SUCCESS".equals(errInfo));
    }

    /**
     * 构建付款码支付请求参数
     */
    private UnionPayCodePayRequest buildCodePayRequest(String orderId, Long amount, String payCode, 
                                                      UnionPayMerchantConfig merchantConfig) {
        UnionPayCodePayRequest request = new UnionPayCodePayRequest();
        request.setMerchantCode(merchantConfig.getMid());
        request.setTerminalCode(merchantConfig.getTid());
        request.setTransactionAmount(amount);
        request.setTransactionCurrencyCode("156"); // 人民币
        request.setMerchantOrderId(orderId);
        request.setMerchantRemark("付款码支付");
        request.setPayMode("CODE_SCAN"); // 付款码支付
        request.setPayCode(payCode);
        request.setDeviceType("02"); // POS设备
        
        // 可选参数
        request.setSerialNum("POS" + System.currentTimeMillis());
        request.setStoreId("STORE001");
        request.setOperatorId("OP001");
        
        return request;
    }
    
    /**
     * 发送付款码支付请求
     */
    private Map<String, Object> sendCodePayRequest(UnionPayCodePayRequest request, String detailsId) {
        // 转换为JSON格式
        JSONObject params = (JSONObject) JSON.toJSON(request);

        return sendJsonRequest(config.getCodePayUrl(), params, detailsId);
    }
    
    /**
     * 发送JSON请求
     */
    private Map<String, Object> sendJsonRequest(String url, JSONObject params, String detailsId) {
        try {
            // 生成动态Authorization头
            String authorization = generateDynamicAuthorization(detailsId);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", authorization);
            
            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(params.toJSONString(), headers);
            
            log.info("发送银联请求: url={}, params={}", url, params.toJSONString());
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            
            log.info("银联响应: {}", response.getBody());
            
            // 解析响应
            if (response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> result = JSON.parseObject(response.getBody(), Map.class);
                return result;
            } else {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("errCode", "ERROR");
                errorResult.put("errInfo", "响应为空");
                return errorResult;
            }
            
        } catch (Exception e) {
            log.error("发送银联请求异常: url={}, error={}", url, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("errCode", "ERROR");
            errorResult.put("errInfo", "网络请求异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 生成Authorization头
     * 使用AccessToken作为Authorization
     */
    private String generateDynamicAuthorization(String detailsId) {
        try {
            // 获取商户配置
            UnionPayMerchantConfig merchantConfig = unionPayConfigService.getUnionPayConfig(detailsId);

            // 获取AccessToken作为Authorization
            String accessToken = accessTokenService.getAccessToken(detailsId);
            log.debug("使用AccessToken作为Authorization: {}", accessToken);
            return "OPEN-ACCESS-TOKEN AccessToken=" + accessToken + ",AppId=" + merchantConfig.getAppId();
        } catch (Exception e) {
            log.error("获取AccessToken失败，使用配置的appSecret", e);
            // 获取商户配置作为备用
            UnionPayMerchantConfig merchantConfig = unionPayConfigService.getUnionPayConfig(detailsId);
            return merchantConfig.getAppSecret();
        }
    }
}
