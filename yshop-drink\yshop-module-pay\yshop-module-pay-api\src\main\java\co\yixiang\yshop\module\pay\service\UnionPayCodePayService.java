package co.yixiang.yshop.module.pay.service;

import java.util.Map;

/**
 * UnionPay付款码支付服务接口
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
public interface UnionPayCodePayService {

    /**
     * 激活终端
     *
     * @param detailsId 商户配置ID
     * @return 激活结果
     */
    Map<String, Object> activeTerminal(String detailsId);

    /**
     * 付款码支付
     *
     * @param orderId 商户订单号
     * @param amount 支付金额（分）
     * @param payCode 付款码
     * @param detailsId 商户配置ID
     * @return 支付结果
     */
    Map<String, Object> codePay(String orderId, Long amount, String payCode, String detailsId);
    
    /**
     * 查询订单状态
     * 
     * @param orderId 商户订单号
     * @param detailsId 商户配置ID
     * @return 查询结果
     */
    Map<String, Object> queryOrder(String orderId, String detailsId);
    
    /**
     * 验证支付结果
     *
     * @param response 银联返回结果
     * @return 是否支付成功
     */
    boolean isPaySuccess(Map<String, Object> response);

    /**
     * 验证终端激活结果
     *
     * @param response 银联返回结果
     * @return 是否激活成功
     */
    boolean isActiveSuccess(Map<String, Object> response);
}
