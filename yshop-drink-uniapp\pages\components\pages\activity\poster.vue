<template>
	<!-- #ifdef MP-WEIXIN -->
	<uv-navbar
	  :fixed="false"
	  title="分享好友"
	  left-arrow
	  @leftClick="$onClickLeft"
	/>
	<!-- #endif -->
  <view class="poster-page">


    <!-- 海报预览区域 -->
    <view class="poster-container">
      <view class="poster-wrapper" v-if="posterImagePath && !generating">
        <!-- 显示生成的海报图片 -->
        <image
          :src="posterImagePath"
          class="poster-image"
          mode="aspectFit"
          @error="onImageError"
        />
      </view>

      <!-- 加载中状态 -->
      <view class="loading-state" v-if="generating">
        <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
        <text class="loading-tip">正在生成海报，请稍候...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!posterImagePath && !generating">
        <uni-icons type="image" size="80" color="#ccc"></uni-icons>
        <text class="empty-text">海报生成失败</text>
        <button class="retry-btn" @click="generatePoster">重新生成</button>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" v-if="posterImagePath && !generating">
      <button class="btn btn-save" @click="savePoster">
        <uni-icons type="download" size="18" color="white"></uni-icons>
        <text>保存到相册</text>
      </button>
      <button class="btn btn-share" @click="sharePoster">
        <uni-icons type="redo" size="18" color="white"></uni-icons>
        <text>分享海报</text>
      </button>
    </view>


    <!-- 海报信息 -->
    <view class="poster-info" v-if="activityInfo">
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">{{ activityInfo.name }}</text>
        </view>
        <view class="card-content">
          <view class="info-desc">
            <text>海报已生成，您可以保存到相册或直接分享给好友</text>
          </view>
          <view class="share-tips">
            <uni-icons type="info" size="14" color="#007aff"></uni-icons>
            <text>好友扫描海报中的二维码即可参与活动</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { generatePoster, getActivityDetail } from '@/api/activity'

export default {
  data() {
    return {
      activityId: null,
      activityInfo: null,
      posterData: null,
      generating: false,
      loadingText: {
        contentText: {
          contentdown: '生成海报中...',
          contentrefresh: '生成海报中...',
          contentnomore: '生成完成'
        }
      },
      shareUrl: '',
      posterImagePath: '',
      posterPalette: {} // painter组件的配置
    }
  },
  
  onLoad(options) {
    this.activityId = options.activityId
    this.loadActivityInfo()
    this.generatePoster()
  },
  
  methods: {
    // 加载活动信息
    async loadActivityInfo() {
      try {
        const res = await getActivityDetail(this.activityId)
        this.activityInfo = res
      } catch (error) {
        console.error('加载活动信息失败:', error)
      }
    },
    
    // 生成海报
    async generatePoster() {
      this.generating = true

      try {
        // 调用后端API生成海报数据
        const res = await generatePoster(this.activityId)

        if (res && res.posterUrl) {
          this.posterData = res
          this.shareUrl = res.shareUrl
          this.posterImagePath = res.posterUrl
          
        } else {
          throw new Error('海报生成失败，请重试')
        }
        this.generating = false

      } catch (error) {
        console.error('生成海报失败:', error)
        uni.showToast({
          title: error.message || '生成海报失败',
          icon: 'none'
        })
        this.generating = false
      }
    },

    // 图片加载错误
    onImageError(error) {
      console.error('海报图片加载失败:', error)
      
      // 显示重新生成选项
      uni.showModal({
        title: '图片加载失败',
        content: '海报图片无法显示，是否重新生成？',
        confirmText: '重新生成',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.regeneratePoster()
          }
        }
      })
    },

    // 构建海报配置（保留原有方法，以防需要）
    buildPosterPalette() {
      this.posterPalette = {
        width: '375px',
        height: '667px',
        background: '#ffffff',
        views: [
          // 背景图片
          {
            type: 'image',
            url: this.posterData.activityImage || this.activityInfo.image,
            css: {
              top: '0px',
              left: '0px',
              width: '375px',
              height: '300px',
              borderRadius: '0px'
            }
          },
          // 活动标题
          {
            type: 'text',
            text: this.posterData.activityTitle || this.activityInfo.name,
            css: {
              top: '320px',
              left: '20px',
              width: '335px',
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#333333',
              textAlign: 'center',
              lineHeight: '32px',
              maxLines: 2
            }
          },
          // 活动描述
          {
            type: 'text',
            text: this.posterData.activityDesc || this.activityInfo.content,
            css: {
              top: '380px',
              left: '20px',
              width: '335px',
              fontSize: '16px',
              color: '#666666',
              textAlign: 'center',
              lineHeight: '24px',
              maxLines: 3
            }
          },
          // 奖励信息
          {
            type: 'text',
            text: '丰厚奖励等你来拿！',
            css: {
              top: '450px',
              left: '20px',
              width: '335px',
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#ff6b35',
              textAlign: 'center'
            }
          },
          // 二维码
          {
            type: 'image',
            url: this.posterData.qrCodeImage,
            css: {
              top: '480px',
              left: '127.5px',
              width: '120px',
              height: '120px',
              borderRadius: '8px'
            }
          },
          // 底部提示文字
          {
            type: 'text',
            text: '长按识别二维码参与活动',
            css: {
              top: '620px',
              left: '20px',
              width: '335px',
              fontSize: '14px',
              color: '#999999',
              textAlign: 'center'
            }
          }
        ]
      }
    },


    
    // 保存海报到相册
    async savePoster() {
      if (!this.posterImagePath) {
        uni.showToast({
          title: '海报还未生成完成',
          icon: 'none'
        })
        return
      }

      try {
        // 显示加载提示
        uni.showLoading({
          title: '保存中...'
        })

        // #ifdef MP-WEIXIN
        // 微信小程序环境，跳过图片验证步骤以避免开发工具问题
        console.log('微信小程序环境，直接下载图片')
        // #endif
        
        // #ifndef MP-WEIXIN  
        // 其他环境先验证图片是否可访问
        try {
          await this.validateImageUrl(this.posterImagePath)
        } catch (error) {
          console.warn('图片验证失败，但继续尝试下载:', error)
        }
        // #endif

        // 下载网络图片到本地临时文件
        const downloadResult = await this.downloadImageToLocal(this.posterImagePath)

        if (!downloadResult.success) {
          throw new Error(downloadResult.error || '下载图片失败')
        }

        // 请求保存图片权限
        await this.requestSavePermission()

        // 保存到相册
        await uni.saveImageToPhotosAlbum({
          filePath: downloadResult.tempFilePath
        })

        uni.hideLoading()
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        })

      } catch (error) {
        uni.hideLoading()
        console.error('保存海报失败:', error)
        
        let errorMessage = '保存失败'
        if (error.message) {
          if (error.message.includes('downloadFile')) {
            errorMessage = '图片下载失败，请检查网络连接'
          } else if (error.message.includes('authorize') || error.message.includes('权限')) {
            errorMessage = '需要相册访问权限才能保存图片'
          } else {
            errorMessage = error.message
          }
        }
        
        uni.showModal({
          title: '保存失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        })
      }
    },

    // 验证图片URL是否可访问
    validateImageUrl(imageUrl) {
      return new Promise((resolve, reject) => {
        if (!imageUrl || typeof imageUrl !== 'string') {
          reject(new Error('图片地址无效'))
          return
        }

        // #ifdef MP-WEIXIN
        // 微信小程序环境，使用更安全的验证方式
        console.log('验证图片URL:', imageUrl)
        
        // 直接尝试获取图片信息，如果失败说明图片不可访问
        uni.getImageInfo({
          src: imageUrl,
          success: (res) => {
            console.log('图片验证成功:', res)
            if (res.width > 0 && res.height > 0) {
              resolve(res)
            } else {
              reject(new Error('图片信息异常'))
            }
          },
          fail: (error) => {
            console.error('图片验证失败:', error)
            // 在开发环境中，可能因为工具问题导致验证失败，但图片实际可用
            // #ifdef MP-WEIXIN
            if (process.env.NODE_ENV === 'development') {
              console.warn('开发环境图片验证失败，跳过验证:', error)
              resolve({ width: 400, height: 600 }) // 模拟验证成功
              return
            }
            // #endif
            reject(new Error('图片无法访问或已失效'))
          }
        })
        // #endif
        
        // #ifndef MP-WEIXIN
        // 其他环境的验证逻辑
        uni.getImageInfo({
          src: imageUrl,
          success: (res) => {
            if (res.width > 0 && res.height > 0) {
              resolve(res)
            } else {
              reject(new Error('图片信息异常'))
            }
          },
          fail: (error) => {
            console.error('图片验证失败:', error)
            reject(new Error('图片无法访问或已失效'))
          }
        })
        // #endif
      })
    },

    // 下载网络图片到本地
    downloadImageToLocal(imageUrl) {
      return new Promise((resolve) => {
        // 检查URL是否有效
        if (!imageUrl || typeof imageUrl !== 'string') {
          resolve({
            success: false,
            error: '图片地址无效'
          })
          return
        }

        // 如果已经是本地路径，直接返回
        if (imageUrl.startsWith('file://') || imageUrl.startsWith('/')) {
          resolve({
            success: true,
            tempFilePath: imageUrl
          })
          return
        }

        console.log('开始下载图片:', imageUrl)

        // #ifdef MP-WEIXIN
        // 微信小程序环境的下载处理
        uni.downloadFile({
          url: imageUrl,
          timeout: 30000, // 30秒超时，避免过长等待
          success: (res) => {
            console.log('下载结果:', res)
            if (res.statusCode === 200 && res.tempFilePath) {
              // 在微信开发者工具中，直接认为下载成功
              // 不进行文件验证，避免 ENOENT 错误
              resolve({
                success: true,
                tempFilePath: res.tempFilePath
              })
            } else {
              resolve({
                success: false,
                error: `下载失败，HTTP状态码: ${res.statusCode}`
              })
            }
          },
          fail: (error) => {
            console.error('下载图片失败:', error)
            
            // 检查是否是开发环境的工具问题
            if (error.errMsg && error.errMsg.includes('ENOENT')) {
              console.warn('检测到开发工具文件系统问题，尝试跳过下载步骤')
              // 开发环境下，如果是文件系统问题，直接使用原URL
              resolve({
                success: true,
                tempFilePath: imageUrl // 直接使用网络URL
              })
              return
            }
            
            let errorMessage = '网络下载失败'
            if (error.errMsg) {
              if (error.errMsg.includes('timeout')) {
                errorMessage = '下载超时，请检查网络连接'
              } else if (error.errMsg.includes('fail')) {
                errorMessage = '下载失败，请重试'
              }
            }
            resolve({
              success: false,
              error: errorMessage
            })
          }
        })
        // #endif
        
        // #ifndef MP-WEIXIN
        // 其他环境的下载处理
        uni.downloadFile({
          url: imageUrl,
          timeout: 60000, // 60秒超时
          success: (res) => {
            console.log('下载结果:', res)
            if (res.statusCode === 200 && res.tempFilePath) {
              // 验证下载的文件是否有效
              uni.getFileInfo({
                filePath: res.tempFilePath,
                success: (fileInfo) => {
                  if (fileInfo.size > 0) {
                    resolve({
                      success: true,
                      tempFilePath: res.tempFilePath
                    })
                  } else {
                    resolve({
                      success: false,
                      error: '下载的文件为空'
                    })
                  }
                },
                fail: () => {
                  // 如果获取文件信息失败，也认为下载成功（兼容性处理）
                  resolve({
                    success: true,
                    tempFilePath: res.tempFilePath
                  })
                }
              })
            } else {
              resolve({
                success: false,
                error: `下载失败，HTTP状态码: ${res.statusCode}`
              })
            }
          },
          fail: (error) => {
            console.error('下载图片失败:', error)
            let errorMessage = '网络下载失败'
            if (error.errMsg) {
              if (error.errMsg.includes('timeout')) {
                errorMessage = '下载超时，请检查网络连接'
              } else if (error.errMsg.includes('fail')) {
                errorMessage = '下载失败，请重试'
              }
            }
            resolve({
              success: false,
              error: errorMessage
            })
          }
        })
        // #endif
      })
    },

    // 请求保存图片权限
    requestSavePermission() {
      return new Promise((resolve, reject) => {
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            resolve()
          },
          fail: () => {
            // 权限被拒绝，引导用户手动开启
            uni.showModal({
              title: '授权提示',
              content: '需要授权访问相册才能保存图片，请在设置中开启相册权限',
              confirmText: '去设置',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.writePhotosAlbum']) {
                        resolve()
                      } else {
                        reject(new Error('用户未开启相册权限'))
                      }
                    },
                    fail: () => {
                      reject(new Error('打开设置失败'))
                    }
                  })
                } else {
                  reject(new Error('用户取消授权'))
                }
              }
            })
          }
        })
      })
    },
    
    // 分享海报
    async sharePoster() {
      if (!this.posterImagePath) {
        uni.showToast({
          title: '海报还未生成完成',
          icon: 'none'
        })
        return
      }

      try {
        // #ifdef MP-WEIXIN
        // 小程序环境下，跳过验证直接下载图片
        uni.showLoading({
          title: '准备分享...'
        })

        console.log('微信小程序环境，直接下载分享图片')

        const downloadResult = await this.downloadImageToLocal(this.posterImagePath)
        uni.hideLoading()

        if (!downloadResult.success) {
          throw new Error(downloadResult.error || '下载图片失败')
        }

        // 使用本地临时文件路径分享
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 2, // 图片
          imageUrl: downloadResult.tempFilePath,
          success: () => {
            uni.showToast({
              title: '分享成功',
              icon: 'success'
            })
          },
          fail: (error) => {
            console.error('分享失败:', error)
            uni.showModal({
              title: '分享失败',
              content: '无法调用分享功能，请稍后重试',
              showCancel: false,
              confirmText: '知道了'
            })
          }
        })
        // #endif

        // #ifndef MP-WEIXIN
        // 非小程序环境，先验证后分享
        uni.showLoading({
          title: '准备分享...'
        })

        // 验证图片是否可访问
        try {
          await this.validateImageUrl(this.posterImagePath)
        } catch (error) {
          console.warn('图片验证失败，但继续尝试分享:', error)
        }

        uni.hideLoading()

        // 直接使用网络地址
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 2, // 图片
          imageUrl: this.posterImagePath,
          success: () => {
            uni.showToast({
              title: '分享成功',
              icon: 'success'
            })
          },
          fail: (error) => {
            console.error('分享失败:', error)
            uni.showModal({
              title: '分享失败',
              content: '无法调用分享功能，请稍后重试',
              showCancel: false,
              confirmText: '知道了'
            })
          }
        })
        // #endif

      } catch (error) {
        uni.hideLoading()
        console.error('分享海报失败:', error)
        
        let errorMessage = '分享失败'
        if (error.message) {
          if (error.message.includes('无法访问')) {
            errorMessage = '海报图片无法访问，请重新生成海报'
          } else if (error.message.includes('下载')) {
            errorMessage = '下载图片失败，请检查网络连接'
          } else {
            errorMessage = error.message
          }
        }
        
        uni.showModal({
          title: '分享失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        })
      }
    },

    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.poster-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
  
  .nav-left {
    display: flex;
    align-items: center;
    
    .nav-text {
      margin-left: 10rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .nav-right {
    width: 100rpx;
  }
}

.poster-container {
  padding: 40rpx;
  display: flex;
  justify-content: center;
}

.poster-wrapper {
  position: relative;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);

  .poster-image {
    width: 375px;
    height: 667px;
    display: block;
  }
}



.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .loading-tip {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .retry-btn {
    padding: 20rpx 40rpx;
    background: #007aff;
    color: white;
    border-radius: 40rpx;
    font-size: 26rpx;
    border: none;
  }
}

.action-buttons {
  display: flex;
  gap: 30rpx;
  padding: 40rpx;
  
  .btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      margin-left: 10rpx;
    }
    
    &.btn-save {
      background: linear-gradient(90deg, #34c759, #30d158);
      color: white;
    }
    
    &.btn-share {
      background: linear-gradient(90deg, #007aff, #00d4ff);
      color: white;
    }

    &.btn-regenerate {
      background: #fff;
      color: #667eea;
      border: 2rpx solid #667eea;

      &:active {
        background: #f8f9ff;
      }
    }
  }
}

.regenerate-section {
  padding: 0 40rpx 20rpx;

  .btn {
    width: 100%;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
      margin-left: 10rpx;
    }
  }
}

.poster-info {
  padding: 0 40rpx 40rpx;
}

.info-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  
  .card-header {
    margin-bottom: 30rpx;
    
    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .card-content {
    .info-desc {
      font-size: 26rpx;
      color: #666;
      text-align: center;
      margin-bottom: 20rpx;
      line-height: 1.5;
    }

    .share-tips {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15rpx;
      background: #f0f8ff;
      border-radius: 12rpx;

      text {
        margin-left: 10rpx;
        font-size: 24rpx;
        color: #007aff;
      }
    }
  }
}
</style>
