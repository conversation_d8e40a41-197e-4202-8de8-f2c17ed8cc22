package co.yixiang.yshop.module.order.service.storeorder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import co.yixiang.yshop.framework.common.constant.ShopConstants;
import co.yixiang.yshop.framework.common.enums.OrderInfoEnum;
import co.yixiang.yshop.framework.common.enums.PayIdEnum;
import co.yixiang.yshop.framework.common.enums.ShopCommonEnum;
import co.yixiang.yshop.framework.common.exception.ErrorCode;
import co.yixiang.yshop.framework.common.util.object.BeanUtils;
import co.yixiang.yshop.framework.common.util.spring.SpringUtils;
import co.yixiang.yshop.framework.tenant.core.context.TenantContextHolder;
import co.yixiang.yshop.module.card.dal.dataobject.vipcard.VipCardDO;
import co.yixiang.yshop.module.card.service.vipcard.AppVipCardService;
import co.yixiang.yshop.module.coupon.dal.dataobject.couponuser.CouponUserDO;
import co.yixiang.yshop.module.coupon.service.couponuser.AppCouponUserService;
import co.yixiang.yshop.module.desk.controller.app.shopdesk.vo.AppShopDeskVO;
import co.yixiang.yshop.module.desk.dal.dataobject.shopdesk.ShopDeskDO;
import co.yixiang.yshop.module.desk.dal.mysql.shopdesk.ShopDeskMapper;
import co.yixiang.yshop.module.member.controller.app.user.vo.AppUserQueryVo;
import co.yixiang.yshop.module.member.dal.dataobject.user.MemberUserDO;
import co.yixiang.yshop.module.member.dal.dataobject.useraddress.UserAddressDO;
import co.yixiang.yshop.module.member.dal.dataobject.userbill.UserBillDO;
import co.yixiang.yshop.module.member.enums.BillDetailEnum;
import co.yixiang.yshop.module.member.service.user.MemberUserService;
import co.yixiang.yshop.module.member.service.useraddress.AppUserAddressService;
import co.yixiang.yshop.module.member.service.userbill.UserBillService;
import co.yixiang.yshop.module.message.enums.WechatTempateEnum;
import co.yixiang.yshop.module.message.mq.producer.WeixinNoticeProducer;
import co.yixiang.yshop.module.message.redismq.msg.OrderMsg;
import co.yixiang.yshop.module.order.controller.app.order.param.AppOrderParam;
import co.yixiang.yshop.module.order.controller.app.order.param.AppPayParam;
import co.yixiang.yshop.module.order.controller.app.order.vo.AppDeskOrderGoodsVo;
import co.yixiang.yshop.module.order.controller.app.order.vo.AppDeskOrderVo;
import co.yixiang.yshop.module.order.controller.app.order.vo.AppStoreOrderQueryVo;
import co.yixiang.yshop.module.order.controller.app.order.vo.message.CartMsgVo;
import co.yixiang.yshop.module.order.controller.app.order.vo.message.ShopOrderMsgVo;
import co.yixiang.yshop.module.order.convert.storeorder.StoreOrderConvert;
import co.yixiang.yshop.module.order.dal.dataobject.ordernumber.OrderNumberDO;
import co.yixiang.yshop.module.order.dal.dataobject.storecartshare.StoreCartShareDO;
import co.yixiang.yshop.module.order.dal.dataobject.storeorder.StoreOrderDO;
import co.yixiang.yshop.module.order.dal.dataobject.storeordercartinfo.StoreOrderCartInfoDO;
import co.yixiang.yshop.module.order.dal.mysql.ordernumber.OrderNumberMapper;
import co.yixiang.yshop.module.order.dal.mysql.storecartshare.StoreCartShareMapper;
import co.yixiang.yshop.module.order.dal.mysql.storeorder.StoreOrderMapper;
import co.yixiang.yshop.module.order.enums.AppFromEnum;
import co.yixiang.yshop.module.order.enums.OrderLogEnum;
import co.yixiang.yshop.module.order.enums.OrderStatusEnum;
import co.yixiang.yshop.module.order.enums.PayTypeEnum;
import co.yixiang.yshop.module.order.service.storeorder.dto.StatusDto;
import co.yixiang.yshop.module.order.service.storeordercartinfo.StoreOrderCartInfoService;
import co.yixiang.yshop.module.order.service.storeorderstatus.StoreOrderStatusService;
import co.yixiang.yshop.module.pay.config.CmbcsWxMiniProgramConfig;
import co.yixiang.yshop.module.pay.config.UnionPayWxMiniProgramConfig;
import co.yixiang.yshop.module.pay.dal.dataobject.merchantdetails.MerchantDetailsDO;
import co.yixiang.yshop.module.pay.service.merchantdetails.MerchantDetailsService;
import co.yixiang.yshop.module.pay.service.UnionPayWxMiniProgramService;
import co.yixiang.yshop.module.pay.service.CmbcsWxMiniProgramService;
import co.yixiang.yshop.module.pay.service.UnionPayCodePayService;
import co.yixiang.yshop.module.pay.dto.CmbcsWxMiniProgramOrderRequest;
import co.yixiang.yshop.module.pay.dto.UnionPayWxMiniProgramOrderRequest;
import co.yixiang.yshop.module.pay.dto.UnionPayWxMiniProgramOrderResponse;
import co.yixiang.yshop.module.pay.util.TestHelper;
import co.yixiang.yshop.module.product.controller.app.cart.vo.AppStoreCartQueryVo;
import co.yixiang.yshop.module.product.dal.dataobject.storeproduct.StoreProductDO;
import co.yixiang.yshop.module.product.dal.dataobject.storeproductattrvalue.StoreProductAttrValueDO;
import co.yixiang.yshop.module.product.service.storeproduct.AppStoreProductService;
import co.yixiang.yshop.module.product.service.storeproductattrvalue.StoreProductAttrValueService;
import co.yixiang.yshop.module.score.dal.dataobject.scoreorder.ScoreOrderDO;
import co.yixiang.yshop.module.score.dal.mysql.scoreorder.ScoreOrderMapper;
import co.yixiang.yshop.module.shop.dal.dataobject.recharge.RechargeDO;
import co.yixiang.yshop.module.shop.service.recharge.AppRechargeService;
import co.yixiang.yshop.module.store.convert.storeshop.StoreShopConvert;
import co.yixiang.yshop.module.store.dal.dataobject.storerevenue.StoreRevenueDO;
import co.yixiang.yshop.module.store.dal.dataobject.storeshop.StoreShopDO;
import co.yixiang.yshop.module.store.dal.mysql.storerevenue.StoreRevenueMapper;
import co.yixiang.yshop.module.store.service.storeshop.AppStoreShopService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.egzosn.pay.spring.boot.core.MerchantPayServiceManager;
import com.egzosn.pay.spring.boot.core.PayServiceConfigurer;
import com.egzosn.pay.spring.boot.core.PayServiceManager;
import com.egzosn.pay.spring.boot.core.bean.MerchantPayOrder;
import com.egzosn.pay.spring.boot.core.merchant.PaymentPlatformMerchantDetails;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static co.yixiang.yshop.framework.common.exception.util.ServiceExceptionUtil.exception;
import static co.yixiang.yshop.module.member.enums.ErrorCodeConstants.COUPON_NOT_CONDITION;
import static co.yixiang.yshop.module.member.enums.ErrorCodeConstants.USER_ADDRESS_NOT_EXISTS;
import static co.yixiang.yshop.module.order.enums.ErrorCodeConstants.*;
import co.yixiang.yshop.framework.tenant.core.aop.TenantIgnore;

/**
 * 订单 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AppStoreOrderServiceImpl extends ServiceImpl<StoreOrderMapper, StoreOrderDO>
        implements AppStoreOrderService {

    @Resource
    private StoreOrderMapper storeOrderMapper;

    @Resource
    private AppUserAddressService appUserAddressService;
    @Resource
    private MemberUserService userService;
    @Resource
    private AppStoreProductService appStoreProductService;
    @Resource
    private StoreOrderCartInfoService storeOrderCartInfoService;
    @Resource
    private StoreOrderStatusService storeOrderStatusService;
    @Resource
    private UserBillService billService;
    @Resource
    private PayServiceManager manager;
    @Resource
    private WeixinNoticeProducer weixinNoticeProducer;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private StoreProductAttrValueService storeProductAttrValueService;
    @Resource
    private AppStoreShopService appStoreShopService;
    @Resource
    private OrderNumberMapper orderNumberMapper;
    @Resource
    private AppCouponUserService appCouponUserService;
    @Resource
    private AsyncStoreOrderService asyncStoreOrderService;
    @Resource
    private MerchantDetailsService merchantDetailsService;
    @Resource
    private AppRechargeService appRechargeService;
    @Resource
    private ShopDeskMapper shopDeskMapper;
    @Resource
    private AppVipCardService appVipCardService;
    @Resource
    private StoreRevenueMapper storeRevenueMapper;
    @Resource
    private ScoreOrderMapper scoreOrderMapper;
    @Resource
    private StoreCartShareMapper storeCartShareMapper;
    @Resource
    private UnionPayWxMiniProgramService unionPayWxMiniProgramService;
    @Resource
    private CmbcsWxMiniProgramService cmbcsWxMiniProgramService;
    @Resource
    private UnionPayCodePayService unionPayCodePayService;
    @Autowired
    private UnionPayWxMiniProgramConfig config;
    @Autowired
    private CmbcsWxMiniProgramConfig cmbcsWxMiniProgramConfig;
    private static final String LOCK_KEY = "cart:check:stock:lock";
    private static final String STOCK_LOCK_KEY = "cart:do:stock:lock";

    /**
     * 订单信息
     *
     * @param unique 唯一值或者单号
     * @param uid    用户id
     * @return YxStoreOrderQueryVo
     */
    @Override
    public AppStoreOrderQueryVo getOrderInfo(String unique, Long uid) {
        LambdaQueryWrapper<StoreOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(
                i -> i.eq(StoreOrderDO::getOrderId, unique).or().eq(StoreOrderDO::getUnique, unique).or()
                        .eq(StoreOrderDO::getExtendOrderId, unique));
        // if (uid != null) {
        // wrapper.eq(StoreOrderDO::getUid, uid);
        // }

        AppStoreOrderQueryVo appStoreOrderQueryVo = StoreOrderConvert.INSTANCE
                .convert1(storeOrderMapper.selectOne(wrapper));
        return appStoreOrderQueryVo;
    }

    /**
     * 创建订单
     *
     * @param uid   用户uid
     * @param param param
     * @return YxStoreOrder
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> createOrder(Long uid, AppOrderParam param) {
        // 转换参数
        List<String> productIds = param.getProductId();
        List<String> numbers = param.getNumber();
        List<String> specs = param.getSpec();
        List<Long> userIdList = new ArrayList<>();
        if ((param.getIsAdmin() == null || !param.getIsAdmin())
                && OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(param.getOrderType())) {
            if (ObjectUtil.isNull(param.getDeskId())) {
                throw exception(STORE_ORDER_DESK_NOT);
            }
            // 点餐走共享菜单
            List<StoreCartShareDO> storeCartShareDOS = storeCartShareMapper
                    .selectList(new LambdaQueryWrapper<StoreCartShareDO>()
                            .eq(StoreCartShareDO::getShopId, param.getShopId())
                            .eq(StoreCartShareDO::getDeskId, param.getDeskId()));
            if (storeCartShareDOS == null || storeCartShareDOS.isEmpty()) {
                ShopDeskDO shopDeskDO = shopDeskMapper.selectById(param.getDeskId());
                Map<String, Object> map = new HashMap<>();
                map.put("orderId", shopDeskDO.getLastOrderNo());
                map.put("msg", "您的菜单已被朋友提交哦！");
                return map;
                // throw exception(new ErrorCode(202504290,"您的菜单已被朋友提交哦！"));
            }
            // JSONArray content
            List<JSONArray> contents = storeCartShareDOS.stream().map(StoreCartShareDO::getContent)
                    .collect(Collectors.toList());
            productIds = new ArrayList<>();
            numbers = new ArrayList<>();
            specs = new ArrayList<>();
            userIdList = storeCartShareDOS.stream().map(StoreCartShareDO::getUid).collect(Collectors.toList());
            for (JSONArray item : contents) {
                List<CartMsgVo> cartMsgVoList = JSON.parseArray(JSON.toJSONString(item), CartMsgVo.class);
                for (CartMsgVo i : cartMsgVoList) {
                    productIds.add(i.getId().toString());
                }
                for (CartMsgVo i : cartMsgVoList) {
                    numbers.add(i.getNumber().toString());
                }
                for (CartMsgVo i : cartMsgVoList) {
                    if (StrUtil.isBlank(i.getValueStr())) {
                        specs.add("默认");
                    } else {
                        specs.add(i.getValueStr());
                    }

                }

            }

        }

        Integer totalNum = 0;
        List<String> cartIds = new ArrayList<>();

        StoreShopDO storeShopDO = appStoreShopService.getById(param.getShopId());

        BigDecimal sumPrice = BigDecimal.ZERO;
        BigDecimal couponPrice = BigDecimal.ZERO;
        BigDecimal postagePrice = storeShopDO.getDeliveryPrice();
        BigDecimal deductionPrice = param.getDeductionPrice() != null ? param.getDeductionPrice() : BigDecimal.ZERO;

        // 对库存检查加锁
        RLock lock = redissonClient.getLock(LOCK_KEY);
        if (lock.tryLock()) {
            try {
                for (int i = 0; i < productIds.size(); i++) {
                    String newSku = StrUtil.replace(specs.get(i), "|", ",");
                    appStoreProductService.checkProductStock(uid, Long.valueOf(productIds.get(i)),
                            Integer.valueOf(numbers.get(i)), newSku);
                    totalNum += Integer.valueOf(numbers.get(i));

                    StoreProductAttrValueDO storeProductAttrValue = storeProductAttrValueService
                            .getOne(Wrappers.<StoreProductAttrValueDO>lambdaQuery()
                                    .eq(StoreProductAttrValueDO::getSku, newSku)
                                    .eq(StoreProductAttrValueDO::getProductId, productIds.get(i)));

                    sumPrice = NumberUtil.add(sumPrice, NumberUtil.mul(numbers.get(i),
                            storeProductAttrValue.getPrice().toString()));
                }

            } catch (Exception ex) {
                log.error("[checkProductStock][执行异常]", ex);
                throw exception(new ErrorCode(999999, ex.getMessage()));
            } finally {
                lock.unlock();
            }
        }

        // 计算优惠券价格
        if (StrUtil.isNotBlank(param.getCouponId())) {
            CouponUserDO couponUserDO = appCouponUserService.getById(param.getCouponId());
            if (couponUserDO != null) {
                if (couponUserDO.getLeast().compareTo(sumPrice) > 0) {
                    throw exception(COUPON_NOT_CONDITION);
                }
                couponPrice = couponUserDO.getValue();

                // 使用了优惠券扣优惠券
                couponUserDO.setStatus(ShopCommonEnum.IS_STATUS_1.getValue());
                appCouponUserService.updateById(couponUserDO);

            }
        }

        // 计算会员折扣价
        if ((StrUtil.isEmpty(param.getUidType()) || param.getUidType().equals("user"))
                && NumberUtil.compare(uid, 0) > 0) {
            MemberUserDO memberUserDO = userService.getById(uid);
            if (memberUserDO.getCardId() != null && memberUserDO.getCardId() > 0) {
                BigDecimal vipPrice = sumPrice
                        .subtract(sumPrice.multiply(NumberUtil.div(memberUserDO.getDiscount() + "", "10")));
                deductionPrice = deductionPrice.add(vipPrice);
            }
        }

        BigDecimal payPrice = BigDecimal.ZERO;
        // 计算最终支付价格
        if (OrderLogEnum.ORDER_TAKE_OUT.getValue().equals(param.getOrderType())) {
            payPrice = NumberUtil.sub(NumberUtil.add(sumPrice, postagePrice), couponPrice, deductionPrice);
        } else {
            payPrice = NumberUtil.sub(sumPrice, couponPrice, deductionPrice);
        }
        // BigDecimal payPrice =
        // NumberUtil.sub(NumberUtil.add(sumPrice,postagePrice),couponPrice);

        // 计算奖励积分
        BigDecimal gainIntegral = this.getGainIntegral(productIds);

        StoreOrderDO storeOrder = new StoreOrderDO();
        String orderSn = "";
        // 判断是否是加餐
        if (OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(param.getOrderType())
                && StrUtil.isNotEmpty(param.getOrderId())) {
            storeOrder = this.getOne(new LambdaQueryWrapper<StoreOrderDO>()
                    .eq(StoreOrderDO::getOrderId, param.getOrderId())
                    .eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_0.getValue()));
            if (storeOrder == null) {
                throw exception(STORE_ORDER_NOT_EXISTS);
            }
            orderSn = param.getOrderId();

            storeOrder.setTotalNum(totalNum + storeOrder.getTotalNum());
            storeOrder.setTotalPrice(sumPrice.add(storeOrder.getTotalPrice()));
            storeOrder.setCouponPrice(couponPrice.add(storeOrder.getCouponPrice()));
            storeOrder.setPayPrice(payPrice.add(storeOrder.getPayPrice()));
            storeOrder.setGainIntegral(gainIntegral.add(storeOrder.getGainIntegral()));
            storeOrder.setCreateTime(LocalDateTime.now());
            storeOrder.setStatus(OrderInfoEnum.STATUS_0.getValue());
            if (StrUtil.isEmpty(param.getUidType()) || param.getUidType().equals("user")) {
                if (StrUtil.isNotEmpty(storeOrder.getUserIds())) {
                    List<String> userids = StrUtil.split(storeOrder.getUserIds(), ",");
                    List<Long> userIdsLong = userids.stream().map(Long::valueOf).collect(Collectors.toList());
                    userIdsLong.addAll(userIdList);
                    Set<Long> set = new HashSet<>(userIdsLong);// 去重复
                    storeOrder.setUserIds(CollUtil.join(set, ","));
                } else {
                    storeOrder.setUserIds(CollUtil.join(userIdList, ","));
                }

                // Set<Long> set = new HashSet<>(userIdsLong);//去重复
                // storeOrder.setUserIds(CollUtil.join(set,","));
                // if(!userids.contains(uid+"")){
                // storeOrder.setUserIds(storeOrder.getUserIds() + "," + uid);
                // }
            }
            this.updateById(storeOrder);
        } else {
            // 如果是银联，订单号前缀要加上payType
            if (PayTypeEnum.UNION.getValue().equals(param.getPayType())) {
                orderSn = "3F0S" + IdUtil.getSnowflake(0, 0).nextIdStr();
            } else if (PayTypeEnum.CMBCS.getValue().equals(param.getPayType())) {
                orderSn = cmbcsWxMiniProgramConfig.getPlatmerid() + TestHelper.getRandom();
            }else {
                orderSn = IdUtil.getSnowflake(0, 0).nextIdStr();
            }
            // 生成分布式唯一值

            // 添加取餐表
            OrderNumberDO orderNumberDO = OrderNumberDO.builder().orderId(orderSn).build();
            orderNumberMapper.insert(orderNumberDO);

            // 组合数据
            LocalDateTime localDateTime = LocalDateTime.now();
            storeOrder.setGetTime(localDateTime.plusMinutes(param.getGettime()));
            storeOrder.setNumberId(orderNumberDO.getId());
            storeOrder.setShopId(storeShopDO.getId());
            storeOrder.setShopName(storeShopDO.getName());
            if (StrUtil.isNotEmpty(param.getUidType()) && param.getUidType().equals("admin")) {
                // 如果市店员协助点餐此处不存用户ID
                storeOrder.setUid(0L);
                storeOrder.setUserIds("");
            } else {
                storeOrder.setUid(uid);
                if (!userIdList.isEmpty()) {
                    storeOrder.setUserIds(CollUtil.join(userIdList, ","));
                } else {
                    storeOrder.setUserIds(uid + "");
                }

            }

            storeOrder.setOrderId(orderSn);
            // 处理如果是外卖 地址
            if (OrderLogEnum.ORDER_TAKE_OUT.getValue().equals(param.getOrderType())) {
                if (StrUtil.isEmpty(param.getAddressId())) {
                    throw exception(SELECT_ADDRESS);
                }
                UserAddressDO userAddress = appUserAddressService.getById(param.getAddressId());
                if (ObjectUtil.isNull(userAddress)) {
                    throw exception(USER_ADDRESS_NOT_EXISTS);
                }
                storeOrder.setRealName(userAddress.getRealName());
                storeOrder.setUserPhone(userAddress.getPhone());
                storeOrder.setUserAddress(userAddress.getAddress() + " " + userAddress.getDetail());
            }
            storeOrder.setCartId(StrUtil.join(",", cartIds));
            storeOrder.setTotalNum(totalNum);
            storeOrder.setTotalPrice(sumPrice);
            storeOrder.setTotalPostage(storeShopDO.getDeliveryPrice());

            storeOrder.setCouponId(StrUtil.isBlank(param.getCouponId()) ? 0 : Integer.valueOf(param.getCouponId()));
            storeOrder.setCouponPrice(couponPrice);
            storeOrder.setPayPrice(payPrice);
            storeOrder.setPayPostage(storeShopDO.getDeliveryPrice());
            storeOrder.setDeductionPrice(deductionPrice);
            storeOrder.setPaid(OrderInfoEnum.PAY_STATUS_0.getValue());
            storeOrder.setPayType(param.getPayType());
            storeOrder.setUseIntegral(BigDecimal.ZERO);
            storeOrder.setBackIntegral(BigDecimal.ZERO);
            storeOrder.setGainIntegral(gainIntegral);
            storeOrder.setMark(param.getRemark());
            storeOrder.setCost(BigDecimal.ZERO);
            // storeOrder.setUnique(key);
            storeOrder.setShippingType(OrderInfoEnum.SHIPPIING_TYPE_1.getValue());
            storeOrder.setOrderType(param.getOrderType());

            // 扫码点餐
            storeOrder.setDeskId(param.getDeskId());
            storeOrder.setDeskNumber(param.getDeskNumber());
            storeOrder.setDeskPeople(param.getDeskPeople());

            boolean res = this.save(storeOrder);
            if (!res) {
                throw exception(ORDER_GEN_FAIL);
            }
        }

        // 减库存加销量
        this.deStockIncSale(productIds, numbers, specs);

        // 保存购物车商品信息，异步执行
        storeOrderCartInfoService.saveCartInfo(uid, param.getUidType(), storeOrder.getId(), storeOrder.getOrderId(),
                productIds, numbers, specs);

        // 异步更新桌面信息
        if (OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(param.getOrderType())) {
            storeOrderCartInfoService.updateDeskInfo(storeOrder);
            // websocket通信
            asyncStoreOrderService.pubOrderInfo(new ShopOrderMsgVo().setShopId(Long.valueOf(param.getShopId())));
        }

        // 增加状态
        storeOrderStatusService.create(uid, storeOrder.getId(), OrderLogEnum.CREATE_ORDER.getValue(),
                OrderLogEnum.CREATE_ORDER.getDesc());

        // 堂食点餐不需要
        if (!OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(param.getOrderType())) {
            // 加入延时队列，30分钟自动取消
            try {
                RBlockingDeque<Object> blockingDeque = redissonClient
                        .getBlockingDeque(ShopConstants.REDIS_ORDER_OUTTIME_UNPAY_QUEUE);
                RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
                delayedQueue.offer(OrderMsg.builder().orderId(orderSn).build(), ShopConstants.ORDER_OUTTIME_UNPAY,
                        TimeUnit.MINUTES);
                String s = TimeUnit.SECONDS.toSeconds(ShopConstants.ORDER_OUTTIME_UNPAY) + "分钟";
                log.info("添加延时队列成功 ，延迟时间：" + s + "订单编号：" + orderSn);
            } catch (Exception e) {
                log.error("添加延时队列失败：{}", e.getMessage());
            }
        } else {
            // 扫码堂食2个小时自动确认收货
            try {
                RBlockingDeque<Object> blockingDeque = redissonClient
                        .getBlockingDeque(ShopConstants.REDIS_ORDER_DESK_CONFIRM);
                RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
                delayedQueue.offer(OrderMsg.builder().orderId(orderSn).build(), ShopConstants.ORDER_TWO_HOUR,
                        TimeUnit.HOURS);
            } catch (Exception e) {
                log.error("添加延时队列失败：{}", e.getMessage());
            }

        }

        Map<String, Object> map = new HashMap<>();
        map.put("orderId", orderSn);
        return map;
    }

    /**
     * 第三方支付
     * 
     * @param uid   用户id
     * @param param 订单参数
     * @return
     */
    @Override
    public Map<String, Object> pay(Long uid, AppPayParam param) {
        AppStoreOrderQueryVo orderInfo = getOrderInfo(param.getUni(), uid);
        UserBillDO userBillDO = billService.getOne(new LambdaQueryWrapper<UserBillDO>().eq(UserBillDO::getUid, uid)
                .eq(UserBillDO::getExtendField, param.getUni()));
        ScoreOrderDO scoreOrderDO = scoreOrderMapper.selectOne(new LambdaQueryWrapper<ScoreOrderDO>()
                .eq(ScoreOrderDO::getOrderId, param.getUni()));

        if (ObjectUtil.isNull(orderInfo) && ObjectUtil.isNull(userBillDO) && ObjectUtil.isNull(scoreOrderDO)) {
            throw exception(STORE_ORDER_NOT_EXISTS);
        }
        if (ObjectUtil.isNotNull(orderInfo) && orderInfo.getPaid().equals(OrderInfoEnum.PAY_STATUS_1.getValue())) {
            throw exception(ORDER_PAY_FINISH);
        }
        if (ObjectUtil.isNotNull(userBillDO) && userBillDO.getStatus().equals(OrderInfoEnum.PAY_STATUS_1.getValue())) {
            throw exception(ORDER_PAY_FINISH);
        }
        if (ObjectUtil.isNotNull(scoreOrderDO)
                && scoreOrderDO.getHavePaid().equals(OrderInfoEnum.PAY_STATUS_1.getValue())) {
            throw exception(ORDER_PAY_FINISH);
        }

        MemberUserDO memberUserDO = userService.getUser(uid);
        Map<String, Object> map = new LinkedHashMap<>();
        BigDecimal price = BigDecimal.ZERO;
        String msg = "";
        String detailsId = "";
        if (orderInfo != null) {
            price = orderInfo.getPayPrice();
            msg = "商品购买";
        } else if (userBillDO != null) {
            if (BillDetailEnum.TYPE_11.getValue().equals(userBillDO.getType())) {
                // 会员卡购买
                VipCardDO vipCardDO = appVipCardService.getById(userBillDO.getLinkId());
                price = vipCardDO.getPrice();
                msg = "会员卡购买";
            } else if (BillDetailEnum.TYPE_1.getValue().equals(userBillDO.getType())) {
                // 充值
                RechargeDO rechargeDO = appRechargeService.getById(userBillDO.getLinkId());
                price = rechargeDO.getSellPrice();
                msg = "用户充值";
            }
        } else if (scoreOrderDO != null) {
            price = scoreOrderDO.getPrice();
            msg = "积分商品兑换购买";
        }
        Long tenantId = TenantContextHolder.getTenantId();
        switch (PayTypeEnum.toType(param.getPaytype())) {
            case WEIXIN:
                if (AppFromEnum.H5.getValue().equals(param.getFrom())) {
                    detailsId = PayIdEnum.WX_WECHAT.getValue() + tenantId;
                    // todo 如果启用微信H5支付充值 需要另外增加一个配置用于同步跳转页面 比如下面的增加了一个id=5的配置,微信支付公众号与H%配置是一样 基本
                    // if(orderInfo != null) {
                    // detailsId = "4";
                    // }else{
                    // detailsId = "5";
                    // }
                    MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, "MWEB", msg,
                            msg, price, param.getUni());

                    Map<String, Object> payOrderInfo = manager.getOrderInfo(payOrder);
                    MerchantDetailsDO merchantDetailsDO = merchantDetailsService.getMerchantDetails(detailsId);
                    String url = merchantDetailsDO.getReturnUrl();

                    String newUrl = "";
                    try {
                        newUrl = String.format("%s%s", payOrderInfo.get("mweb_url"),
                                "&redirect_url=" + URLEncoder.encode(url, "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        log.error(e.getMessage());
                    }
                    map.put("data", newUrl);
                    map.put("trade_type", "MWEB");
                } else if (AppFromEnum.WECHAT.getValue().equals(param.getFrom())) {
                    // 微信公众号
                    // MerchantPayOrder payOrder = new MerchantPayOrder("4", "JSAPI", msg,
                    // msg, price, param.getUni());
                    MerchantPayOrder payOrder = new MerchantPayOrder(PayIdEnum.WX_WECHAT.getValue() + tenantId, "JSAPI",
                            msg,
                            msg, price, param.getUni());
                    payOrder.setOpenid(memberUserDO.getOpenid());
                    map.put("data", manager.getOrderInfo(payOrder));
                    map.put("trade_type", "W-JSAPI");
                } else if (AppFromEnum.SCAN_PC.getValue().equals(param.getFrom())) {
                    // 付款码支付
                    MerchantPayOrder payOrder = new MerchantPayOrder(PayIdEnum.WX_MINIAPP.getValue() + tenantId,
                            "MICROPAY", msg,
                            msg, price, param.getUni());
                    payOrder.setAuthCode(param.getAuthCode());
                    Map<String, Object> mapRes = manager.getOrderInfo(payOrder);
                    if (mapRes.get("return_code").equals("SUCCESS") && mapRes.get("result_code").equals("SUCCESS")) {
                        paySuccess(mapRes.get("out_trade_no").toString(), param.getPaytype());
                    } else {
                        throw exception(new ErrorCode(20247170, mapRes.get("return_msg").toString()));
                    }
                    map.put("data", "");
                    map.put("trade_type", "MICROPAY");
                } else {// 微信小程序
                    MerchantPayOrder payOrder = new MerchantPayOrder(PayIdEnum.WX_MINIAPP.getValue() + tenantId,
                            "JSAPI", msg,
                            msg, price, param.getUni());
                    payOrder.setOpenid(memberUserDO.getRoutineOpenid());
                    map.put("data", manager.getOrderInfo(payOrder));
                    map.put("trade_type", "JSAPI");

                }
                break;
            case YUE:
                this.yuePay(param.getUni(), uid);
                map.put("status", "ok");
                break;
            case CASH:
                storeOrderMapper.update(StoreOrderDO.builder()
                        .paid(OrderInfoEnum.PAY_STATUS_1.getValue())
                        .payTime(LocalDateTime.now())
                        .status(OrderInfoEnum.STATUS_2.getValue())
                        .build(), new LambdaQueryWrapper<StoreOrderDO>().eq(StoreOrderDO::getOrderId, param.getUni()));
                map.put("status", "ok");
                break;
            case ALI:
                detailsId = PayIdEnum.ALI_H5.getValue() + tenantId;
                if (AppFromEnum.SCAN_PC.getValue().equals(param.getFrom())) {// 付款码支付
                    MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, "BAR_CODE", msg,
                            msg, price, param.getUni());
                    payOrder.setAuthCode(param.getAuthCode());
                    Map<String, Object> mapRes = manager.getOrderInfo(payOrder);
                    if (mapRes.get("code").equals("10000") && mapRes.get("msg").equals("Success")) {
                        paySuccess(mapRes.get("out_trade_no").toString(), param.getPaytype());
                    } else {
                        throw exception(new ErrorCode(20247171, mapRes.get("msg").toString()));
                    }
                    map.put("data", "");
                    map.put("trade_type", "MICROPAY");
                } else {
                    // h5支付

                    // todo 如果启用支付宝H5支付充值 需要另外增加一个配置用于同步跳转页面 比如下面的增加了一个id=6的配置
                    // if(orderInfo != null) {
                    // detailsId = "1";
                    // }else{
                    // detailsId = "6";
                    // }
                    MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, "WAP", msg,
                            msg, price, param.getUni());
                    map.put("data", manager.toPay(payOrder));
                    map.put("trade_type", "JSAPI");
                }
                break;
            case UNION:
                if (AppFromEnum.ROUNTINE.getValue().equals(param.getFrom())) {
                    // 微信小程序银联支付
                    // 使用新的UnionPay微信小程序支付服务
                    try {
                        // 检查用户openid是否存在
                        if (memberUserDO == null || StrUtil.isEmpty(memberUserDO.getRoutineOpenid())) {
                            throw exception(new ErrorCode(20247172, "用户微信小程序openid不存在，无法使用银联微信小程序支付"));
                        }

                        // 创建UnionPay微信小程序支付请求
                        UnionPayWxMiniProgramOrderRequest unionPayRequest = new UnionPayWxMiniProgramOrderRequest(
                                param.getUni(), price, msg,
                                memberUserDO.getRoutineOpenid(),
                                memberUserDO.getNickname(),
                                memberUserDO.getMobile());
                        unionPayRequest.setUserId(memberUserDO.getId().toString());
                        unionPayRequest.setRequestTimestamp(String.valueOf(System.currentTimeMillis()));

                        // 构建detailsId，格式：union_miniapp_wx_{tenantId}
                        detailsId = "union_miniapp_wx_" + tenantId;

                        Map<String, Object> unionPayResponse = unionPayWxMiniProgramService
                                .createOrder(unionPayRequest, detailsId);

                        map.put("data", unionPayResponse);
                        map.put("trade_type", "UNION_WX_MINIPROGRAM");
                    } catch (Exception e) {
                        log.error("银联微信小程序支付异常", e);
                        throw exception(new ErrorCode(20247172, "银联微信小程序支付异常: " + e.getMessage()));
                    }
                } else if (AppFromEnum.SCAN_PC.getValue().equals(param.getFrom())) {
                    // 银联付款码支付
                    
                    try {
                        detailsId = PayIdEnum.UNION_H5.getValue() + tenantId;
                        Map<String, Object> mapRes = unionPayCodePayService.codePay(
                                param.getUni(),
                                price.multiply(new BigDecimal("100")).longValue(), // 转换为分
                                param.getAuthCode(),
                                detailsId
                        );

                        if (unionPayCodePayService.isPaySuccess(mapRes)) {
                            paySuccess(param.getUni(), param.getPaytype());
                            map.put("data", "");
                            map.put("trade_type", "MICROPAY");
                        } else {
                            String errInfo = (String) mapRes.get("errInfo");
                            throw exception(new ErrorCode(20247172, "银联付款码支付失败: " + errInfo));
                        }
                    } catch (Exception e) {
                        log.error("银联付款码支付异常", e);
                        throw exception(new ErrorCode(20247172, "银联付款码支付异常: " + e.getMessage()));
                    }
                    // MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, "WEB", msg,
                    //         msg, price, param.getUni());
                    // payOrder.setAuthCode(param.getAuthCode());
                    // Map<String, Object> mapRes = manager.getOrderInfo(payOrder);
                    // if ("00".equals(mapRes.get("respCode"))) {
                    //     paySuccess(mapRes.get("orderId").toString(), param.getPaytype());
                    // } else {
                    //     throw exception(new ErrorCode(20247172, mapRes.get("respMsg").toString()));
                    // }
                    // map.put("data", "");
                    // map.put("trade_type", "MICROPAY");
                } else {
                    // h5支付
                    detailsId = PayIdEnum.UNION_H5.getValue() + tenantId;
                    MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, "WEB", msg,
                            msg, price, param.getUni());
                    map.put("data", manager.toPay(payOrder));
                }
                break;
            case CMBCS:
                // 民生银行支付
                if (AppFromEnum.ROUNTINE.getValue().equals(param.getFrom())) {
                    // 微信小程序民生银行支付
                    try {
                        // 创建民生银行微信小程序支付请求
                        CmbcsWxMiniProgramOrderRequest cmbcsRequest = new CmbcsWxMiniProgramOrderRequest();
                        cmbcsRequest.setTxnflow(param.getUni());
                        cmbcsRequest.setTranamt(price);
                        cmbcsRequest.setSubject(msg);
                        cmbcsRequest.setOpenid(memberUserDO.getRoutineOpenid());
                        cmbcsRequest.setUserIp("127.0.0.1"); // 可以从请求中获取真实IP
                        cmbcsRequest.setRemark(msg);

                        // 构建detailsId，格式：cmbcs_miniapp_wx_{tenantId}
                        detailsId = "cmbcs_miniapp_wx_" + tenantId;

                        Map<String, Object> cmbcsResponse = cmbcsWxMiniProgramService
                                .createOrder(cmbcsRequest, detailsId);

                        map.put("data", cmbcsResponse);
                        map.put("trade_type", "CMBCS_WX_MINIPROGRAM");
                    } catch (Exception e) {
                        log.error("民生银行微信小程序支付异常", e);
                        throw exception(new ErrorCode(20247173, "民生银行微信小程序支付异常: " + e.getMessage()));
                    }
                } else {
                    // 其他环境暂不支持民生银行支付
                    throw exception(new ErrorCode(20247174, "当前环境暂不支持民生银行支付"));
                }
                break;
            default:
        }
        return map;
    }

    /**
     * 余额支付
     *
     * @param orderId 订单号
     * @param uid     用户id
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void yuePay(String orderId, Long uid) {
        AppStoreOrderQueryVo orderInfo = getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(orderInfo)) {
            throw exception(STORE_ORDER_NOT_EXISTS);
        }

        if (OrderInfoEnum.PAY_STATUS_1.getValue().equals(orderInfo.getPaid())) {
            throw exception(ORDER_PAY_FINISH);
        }

        AppUserQueryVo userInfo = userService.getAppUser(uid);

        if (userInfo.getNowMoney().compareTo(orderInfo.getPayPrice()) < 0) {
            throw exception(PAY_YUE_NOT);
        }

        userService.decPrice(uid, orderInfo.getPayPrice());

        // 支付成功后处理
        this.paySuccess(orderInfo.getOrderId(), PayTypeEnum.YUE.getValue());
    }

    /**
     * 支付成功后操作
     *
     * @param orderId 订单号
     * @param payType 支付方式
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @TenantIgnore
    public void paySuccess(String orderId, String payType) {
        // 处理充值与会员卡订单
        UserBillDO userBillDO = billService.getOne(new LambdaQueryWrapper<UserBillDO>()
                .eq(UserBillDO::getExtendField, orderId));
        ScoreOrderDO scoreOrderDO = scoreOrderMapper.selectOne(new LambdaQueryWrapper<ScoreOrderDO>()
                .eq(ScoreOrderDO::getOrderId, orderId));
        if (userBillDO != null) {
            userBillDO.setStatus(ShopCommonEnum.IS_STATUS_1.getValue());
            billService.updateById(userBillDO);
            if (BillDetailEnum.TYPE_11.getValue().equals(userBillDO.getType())) {
                // 会员卡购买
                VipCardDO vipCardDO = appVipCardService.getById(userBillDO.getLinkId());
                MemberUserDO memberUserDO = userService.getById(userBillDO.getUid());
                memberUserDO.setCardId(vipCardDO.getId());
                memberUserDO.setCardName(vipCardDO.getName());
                memberUserDO.setDiscount(vipCardDO.getDiscount());
                memberUserDO.setNowMoney(memberUserDO.getNowMoney().add(vipCardDO.getMony()));
                memberUserDO.setIntegral(memberUserDO.getIntegral().add(new BigDecimal(vipCardDO.getIntegral())));
                userService.updateById(memberUserDO);
                if (vipCardDO.getMony().intValue() > 0) {
                    String mark = "购买会员卡，系统增加了" + vipCardDO.getMony() + "余额";
                    // BigDecimal newMoney = memberUserDO.getNowMoney().add(vipCardDO.getMony());
                    billService.income(memberUserDO.getId(), "系统增加余额", BillDetailEnum.CATEGORY_1.getValue(),
                            BillDetailEnum.TYPE_6.getValue(), vipCardDO.getMony().doubleValue(),
                            memberUserDO.getNowMoney().doubleValue(), mark, "");
                }
                if (vipCardDO.getIntegral() > 0) {
                    String mark = "购买会员卡，系统增加了" + vipCardDO.getIntegral() + "积分";
                    // BigDecimal newIntegral = memberUserDO.getIntegral().add(new
                    // BigDecimal(vipCardDO.getIntegral()));
                    billService.income(memberUserDO.getId(), "系统增加积分", BillDetailEnum.CATEGORY_2.getValue(),
                            BillDetailEnum.TYPE_6.getValue(), Double.valueOf(vipCardDO.getIntegral()),
                            memberUserDO.getIntegral().doubleValue(), mark, "");
                }
            } else if (BillDetailEnum.TYPE_1.getValue().equals(userBillDO.getType())) {
                // 充值
                userService.incMoney(userBillDO.getUid(), userBillDO.getNumber());
            }

            return;
        } else if (scoreOrderDO != null) {
            // 处理积分订单
            MemberUserDO userInfo = userService.getUser(scoreOrderDO.getUid());
            // 减去积分
            userService.decScore(scoreOrderDO.getUid(), scoreOrderDO.getScore());

            scoreOrderDO.setHavePaid(OrderInfoEnum.PAY_STATUS_1.getValue());
            scoreOrderMapper.updateById(scoreOrderDO);

            // 增加流水
            billService.expend(scoreOrderDO.getUid(), "积分兑换",
                    BillDetailEnum.CATEGORY_2.getValue(),
                    BillDetailEnum.TYPE_3.getValue(),
                    scoreOrderDO.getScore().doubleValue(), userInfo.getIntegral().doubleValue(),
                    scoreOrderDO.getScore() + "积分兑换商品");

            return;
        }

        log.info("orderId:[{}]", orderId);
        AppStoreOrderQueryVo orderInfo = getOrderInfo(orderId, null);
        log.info("orderInfo:[{}]", orderInfo);
        if (orderInfo == null) {
            return;
        }

        // 更新订单状态
        LambdaQueryWrapper<StoreOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreOrderDO::getOrderId, orderId);
        StoreOrderDO storeOrder = new StoreOrderDO();
        storeOrder.setPaid(OrderInfoEnum.PAY_STATUS_1.getValue());
        storeOrder.setPayType(payType);
        storeOrder.setPayTime(LocalDateTime.now());
        // 如果是堂食 最后付款后直接完成
        if (OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(orderInfo.getOrderType())) {
            storeOrder.setStatus(OrderInfoEnum.STATUS_3.getValue());
            ShopDeskDO shopDeskDO = new ShopDeskDO();
            shopDeskDO.setLastOrderStatus(OrderInfoEnum.DESK_ORDER_STATUS_CONFIRM.getValue());
            shopDeskMapper.update(shopDeskDO, new LambdaQueryWrapper<ShopDeskDO>()
                    .eq(ShopDeskDO::getId, orderInfo.getDeskId()));
        } else {
            // websocket通信
            asyncStoreOrderService.pubOrderInfo(new ShopOrderMsgVo().setShopId(Long.valueOf(orderInfo.getShopId())));

            // 检查是否需要自动出单
            this.checkAndAddAutoOrderSend(orderInfo);
        }
        this.update(storeOrder, wrapper);

        // 增加用户购买次数
        userService.incPayCount(orderInfo.getUid());
        // 增加状态
        storeOrderStatusService.create(orderInfo.getUid(), orderInfo.getId(), OrderLogEnum.PAY_ORDER_SUCCESS.getValue(),
                OrderLogEnum.PAY_ORDER_SUCCESS.getDesc());

        MemberUserDO userInfo = userService.getUser(orderInfo.getUid());
        // 增加流水
        String payTypeMsg = PayTypeEnum.WEIXIN.getDesc();
        if (PayTypeEnum.YUE.getValue().equals(payType)) {
            payTypeMsg = PayTypeEnum.YUE.getDesc();
        } else if (PayTypeEnum.ALI.getValue().equals(payType)) {
            payTypeMsg = PayTypeEnum.ALI.getDesc();
        }  else if (PayTypeEnum.UNION.getValue().equals(payType)) {
            payTypeMsg = PayTypeEnum.UNION.getDesc();
        } else if (PayTypeEnum.CMBCS.getValue().equals(payType)) {
            payTypeMsg = PayTypeEnum.CMBCS.getDesc();
        }
        billService.expend(userInfo == null ? 0 : userInfo.getId(), "购买商品",
                BillDetailEnum.CATEGORY_1.getValue(),
                BillDetailEnum.TYPE_3.getValue(),
                orderInfo.getPayPrice().doubleValue(), userInfo == null ? 0d : userInfo.getNowMoney().doubleValue(),
                payTypeMsg + orderInfo.getPayPrice() + "元购买商品");

        StoreRevenueDO storeRevenueDO = StoreRevenueDO.builder()
                .shopId(orderInfo.getShopId())
                .shopName(orderInfo.getShopName())
                .uid(orderInfo.getUid())
                .amount(orderInfo.getPayPrice())
                .build();
        // 店铺新增收支明细,由于微信等支付成功通知不能传租户且这里进行里租户忽略 所以手动加下
        storeRevenueDO.setTenantId(storeOrder.getTenantId());
        storeRevenueMapper.insert(storeRevenueDO);

        // 发送消息队列进行推送消息,堂食不需要
        if (!OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(orderInfo.getOrderType()) && userInfo != null &&
                userInfo.getLoginType().equals(AppFromEnum.ROUNTINE.getValue())) {
            List<StoreOrderCartInfoDO> storeOrderCartInfoDOList = storeOrderCartInfoService
                    .list(new LambdaQueryWrapper<StoreOrderCartInfoDO>()
                            .eq(StoreOrderCartInfoDO::getOid, orderInfo.getId()));
            List<String> names = storeOrderCartInfoDOList.stream().map(StoreOrderCartInfoDO::getTitle)
                    .collect(Collectors.toList());
            String productName = StrUtil.join(",", names);
            weixinNoticeProducer.sendNoticeMessage(orderInfo.getUid(), WechatTempateEnum.PAY_SUCCESS.getValue(),
                    WechatTempateEnum.SUBSCRIBE.getValue(), orderInfo.getOrderId(),
                    "", "", "", "", orderInfo.getId(), orderInfo.getNumberId(),
                    productName, orderInfo.getShopName());
        }

    }

    /**
     * 减库存增加销量
     *
     * @param productIds 商品id
     * @param numbers    商品数量
     * @param specs      商品规格
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deStockIncSale(List<String> productIds, List<String> numbers, List<String> specs) {

        log.info("========减库存增加销量start=========");
        // 对库存加锁
        RLock lock = redissonClient.getLock(STOCK_LOCK_KEY);
        if (lock.tryLock()) {
            try {
                for (int i = 0; i < productIds.size(); i++) {
                    String newSku = StrUtil.replace(specs.get(i), "|", ",");
                    appStoreProductService.decProductStock(Integer.valueOf(numbers.get(i)),
                            Long.valueOf(productIds.get(i)),
                            newSku, 0L, "");
                }
            } catch (Exception ex) {
                log.error("[deStockIncSale][执行异常]", ex);
                throw exception(new ErrorCode(999999, ex.getMessage()));
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * 订单列表
     *
     * @param uid       用户id
     * @param orderType 订单类型
     * @param type      OrderStatusEnum
     * @param page      page
     * @param limit     limit
     * @return list
     */
    @Override
    public List<AppStoreOrderQueryVo> orderList(Long uid, String orderType, int type, int page, int limit) {
        LambdaQueryWrapper<StoreOrderDO> wrapper = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapper.apply(
                    "FIND_IN_SET ('" + uid + "',user_ids)");
            // wrapper.eq(StoreOrderDO::getUid, uid);
        }
        wrapper.eq(StoreOrderDO::getOrderType, orderType).orderByDesc(StoreOrderDO::getId);
        // wrapper.eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_1.getValue())
        // .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
        // .eq(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_0.getValue())
        // .orderByDesc(StoreOrderDO::getId);
        if (OrderLogEnum.ORDER_TAKE_DUE.getValue().equals(orderType)) {
            wrapper.eq(StoreOrderDO::getDueStatus, type);
        } else {
            switch (OrderStatusEnum.toType(type)) {
                case STATUS__1:
                    break;
                // 未支付
                case STATUS_0:
                    wrapper.eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_0.getValue())
                            .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
                            .eq(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_0.getValue());
                    break;
                // 已经支付
                case STATUS_1:
                    wrapper.eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_1.getValue())
                            .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
                            .eq(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_0.getValue());
                    break;
                // 待收货
                case STATUS_2:
                    wrapper.eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_1.getValue())
                            .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
                            .eq(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_1.getValue());
                    break;
                // //待评价
                // case STATUS_3:
                // wrapper.eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_1.getValue())
                // .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
                // .eq(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_2.getValue());
                // break;
                // 已完成
                case STATUS_4:
                    wrapper.eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_1.getValue())
                            .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
                            .ge(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_2.getValue());
                    break;
                // 退款
                case STATUS_MINUS_3:
                    String[] strs = { "1", "2" };
                    wrapper.in(StoreOrderDO::getRefundStatus, Arrays.asList(strs));
                    break;
                default:
            }
        }

        Page<StoreOrderDO> pageModel = new Page<>(page, limit);
        IPage<StoreOrderDO> pageList = storeOrderMapper.selectPage(pageModel, wrapper);
        List<AppStoreOrderQueryVo> list = StoreOrderConvert.INSTANCE.convertList01(pageList.getRecords());

        return list.stream().map(this::handleOrder).collect(Collectors.toList());

    }

    public List<StoreOrderCartInfoDO> streamData(Integer i, List<StoreOrderCartInfoDO> cartInfos) {
        List<StoreOrderCartInfoDO> orderCartInfoDOS = cartInfos.stream()
                .filter(o -> i.compareTo(o.getAddProductMark()) == 0)
                .collect(Collectors.toList());

        return orderCartInfoDOS;
    }

    /**
     * 处理订单返回的状态
     *
     * @param order order
     * @return YxStoreOrderQueryVo
     */
    @Override
    public AppStoreOrderQueryVo handleOrder(AppStoreOrderQueryVo order) {
        LambdaQueryWrapper<StoreOrderCartInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreOrderCartInfoDO::getOid, order.getId())
                .orderByDesc(StoreOrderCartInfoDO::getId);
        List<StoreOrderCartInfoDO> cartInfos = storeOrderCartInfoService.list(wrapper);

        order.setCartInfo(cartInfos);

        StoreShopDO storeShopDO = appStoreShopService.getById(order.getShopId());
        order.setShop(StoreShopConvert.INSTANCE.convert02(storeShopDO));

        if (ObjectUtil.isNotNull(order.getDeskId())) {
            ShopDeskDO shopDeskDO = shopDeskMapper.selectById(order.getDeskId());
            order.setAppShopDeskVO(BeanUtils.toBean(shopDeskDO, AppShopDeskVO.class));
        }

        long count = storeOrderMapper.selectCount(new LambdaQueryWrapper<StoreOrderDO>()
                .eq(StoreOrderDO::getShopId, order.getShopId())
                .lt(StoreOrderDO::getCreateTime, order.getCreateTime())
                .eq(StoreOrderDO::getPaid, OrderInfoEnum.PAY_STATUS_1.getValue())
                .eq(StoreOrderDO::getRefundStatus, OrderInfoEnum.REFUND_STATUS_0.getValue())
                .eq(StoreOrderDO::getStatus, OrderInfoEnum.STATUS_0.getValue()));
        order.setPreNum(count);

        StatusDto statusDTO = new StatusDto();
        if (OrderStatusEnum.STATUS_0.getValue().equals(order.getPaid())) {
            // 计算未支付到自动取消订 时间
            int offset = Integer.valueOf(String.valueOf(ShopConstants.ORDER_OUTTIME_UNPAY));
            // Date time = DateUtil.offsetMinute(order.getCreateTime()., offset);
            statusDTO.setYClass("nobuy");
            // statusDTO.setMsg(StrUtil.format("请在{}前完成支付", DateUtil.formatDateTime(time)));
            statusDTO.setType("0");
            statusDTO.setTitle("未支付");
        } else if (OrderInfoEnum.REFUND_STATUS_1.getValue().equals(order.getRefundStatus())) {
            statusDTO.setYClass("state-sqtk");
            statusDTO.setMsg("商家审核中,请耐心等待");
            statusDTO.setType("-1");
            statusDTO.setTitle("申请退款中");
        } else if (OrderInfoEnum.REFUND_STATUS_2.getValue().equals(order.getRefundStatus())) {
            statusDTO.setYClass("state-sqtk");
            statusDTO.setMsg("已为您退款,感谢您的支持");
            statusDTO.setType("-2");
            statusDTO.setTitle("已退款");
        } else if (OrderInfoEnum.STATUS_0.getValue().equals(order.getStatus())) {
            // 拼团 todo
            if (order.getPinkId() > 0) {
            } else {
                statusDTO.setYClass("state-nfh");
                statusDTO.setMsg("商家未发货,请耐心等待");
                statusDTO.setType("1");
                statusDTO.setTitle("制作中");
            }

        } else if (OrderInfoEnum.STATUS_1.getValue().equals(order.getStatus())) {
            if (OrderLogEnum.ORDER_TAKE_OUT.getValue().equals(order.getOrderType())) {
                statusDTO.setTitle("配送中");
            } else {
                statusDTO.setTitle("待取餐");
            }
            statusDTO.setYClass("state-ysh");
            statusDTO.setMsg("服务商已发货");
            statusDTO.setType("2");

        } else if (OrderInfoEnum.STATUS_2.getValue().equals(order.getStatus())) {
            if (OrderLogEnum.ORDER_TAKE_OUT.getValue().equals(order.getOrderType())) {
                statusDTO.setTitle("已收货");
            } else {
                statusDTO.setTitle("已取餐");
            }
            statusDTO.setYClass("state-ypj");
            statusDTO.setMsg("已收货,快去评价一下吧");
            statusDTO.setType("3");
        } else if (OrderInfoEnum.STATUS_3.getValue().equals(order.getStatus())) {
            statusDTO.setYClass("state-ytk");
            statusDTO.setMsg("交易完成,感谢您的支持");
            statusDTO.setType("4");
            statusDTO.setTitle("交易完成");
        }

        if (PayTypeEnum.WEIXIN.getValue().equals(order.getPayType())) {
            statusDTO.setPayType("微信支付");
        } else if (PayTypeEnum.YUE.getValue().equals(order.getPayType())) {
            statusDTO.setPayType("余额支付");
        } else if (PayTypeEnum.ALI.getValue().equals(order.getPayType())) {
            statusDTO.setPayType("支付宝支付");
        } else if (PayTypeEnum.UNION.getValue().equals(order.getPayType())) {
            statusDTO.setPayType("银联支付");
        } else if (PayTypeEnum.CMBCS.getValue().equals(order.getPayType())) {
            statusDTO.setPayType("民生银行支付");
        } else {
            statusDTO.setPayType("积分支付");
        }

        order.setStatusDto(statusDTO);

        // 如果扫描点餐 处理下新订单展示信息
        if (!OrderLogEnum.ORDER_TAKE_DUE.getValue().equals(order.getOrderType()) && cartInfos != null
                && !cartInfos.isEmpty()) {
            List<AppDeskOrderVo> appDeskOrderVos = new ArrayList<>();
            // 获取当前最后加餐次数
            Integer lastAddProductMark = cartInfos.get(0).getAddProductMark();
            while (lastAddProductMark >= 0) {
                List<StoreOrderCartInfoDO> orderCartInfoDOS = streamData(lastAddProductMark, cartInfos);
                AppDeskOrderVo appDeskOrderVo = BeanUtils.toBean(orderCartInfoDOS.get(0), AppDeskOrderVo.class);
                List<AppDeskOrderGoodsVo> appDeskOrderGoodsVos = BeanUtils.toBean(orderCartInfoDOS,
                        AppDeskOrderGoodsVo.class);
                appDeskOrderVo.setAppDeskOrderGoodsVos(appDeskOrderGoodsVos);
                appDeskOrderVos.add(appDeskOrderVo);
                lastAddProductMark--;
            }

            order.setAppDeskOrderVo(appDeskOrderVos);

        }

        return order;
    }

    /**
     * 订单确认收货
     *
     * @param orderId 单号
     * @param uid     uid
     */
    @Override
    public void takeOrder(String orderId, Long uid) {
        AppStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(order)) {
            throw exception(STORE_ORDER_NOT_EXISTS);
        }

        // if (OrderInfoEnum.PAY_STATUS_0.getValue().equals(order.getPaid())) {
        // throw exception(ORDER_STATUS_ERROR);
        // }

        if (OrderInfoEnum.STATUS_3.getValue().equals(order.getStatus())) {
            throw exception(ORDER_STATUS_FINISH);
        }
        order = handleOrder(order);
        // if (order.getOrderType().equals(OrderLogEnum.ORDER_TAKE_OUT.getValue())
        // &&
        // !OrderStatusEnum.STATUS_2.getValue().toString().equals(order.get_status().get_type()))
        // {
        // throw exception(ORDER_STATUS_ERROR);
        // }

        StoreOrderDO storeOrder = new StoreOrderDO();
        storeOrder.setStatus(OrderInfoEnum.STATUS_3.getValue());
        storeOrder.setId(order.getId());
        if (OrderLogEnum.ORDER_TAKE_DESK.getValue().equals(order.getOrderType())) {
            storeOrder.setPaid(OrderInfoEnum.PAY_STATUS_1.getValue());
            storeOrder.setPayTime(LocalDateTime.now());
            ShopDeskDO shopDeskDO = new ShopDeskDO();
            shopDeskDO.setLastOrderStatus(OrderInfoEnum.DESK_ORDER_STATUS_CONFIRM.getValue());
            shopDeskMapper.update(shopDeskDO, new LambdaQueryWrapper<ShopDeskDO>()
                    .eq(ShopDeskDO::getId, order.getDeskId()));

            // 删除当前桌面单共享菜单
            storeCartShareMapper.delete(new LambdaQueryWrapper<StoreCartShareDO>()
                    .eq(StoreCartShareDO::getShopId, order.getShopId())
                    .eq(StoreCartShareDO::getDeskId, order.getDeskId()));
        }
        this.updateById(storeOrder);

        // 增加状态
        storeOrderStatusService.create(order.getUid(), order.getId(), OrderLogEnum.TAKE_ORDER_DELIVERY.getValue(),
                OrderLogEnum.TAKE_ORDER_DELIVERY.getDesc());

        // 奖励积分
        this.gainUserIntegral(order);

        // 分销计算 todo

    }

    /**
     * 申请退款
     *
     * @param explain 退款备注
     * @param Img     图片
     * @param text    理由
     * @param orderId 订单号
     * @param uid     uid
     */
    @Override
    public void orderApplyRefund(String explain, String Img, String text, String orderId, Long uid) {
        AppStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(order)) {
            throw exception(STORE_ORDER_NOT_EXISTS);
        }

        if (OrderInfoEnum.REFUND_STATUS_2.getValue().equals(order.getRefundStatus())) {
            throw exception(ORDER_REFUNDED);
        }
        if (OrderInfoEnum.REFUND_STATUS_1.getValue().equals(order.getRefundStatus())) {
            throw exception(ORDER_REFUNDING);
        }

        StoreOrderDO storeOrder = new StoreOrderDO();
        storeOrder.setRefundStatus(OrderInfoEnum.REFUND_STATUS_1.getValue());
        storeOrder.setRefundReasonTime(LocalDateTime.now());
        storeOrder.setRefundReasonWapExplain(explain);
        storeOrder.setRefundReasonWapImg(Img);
        storeOrder.setRefundReasonWap(text);
        storeOrder.setId(order.getId());
        this.updateById(storeOrder);

        // 增加状态
        storeOrderStatusService.create(order.getUid(), order.getId(),
                OrderLogEnum.REFUND_ORDER_APPLY.getValue(),
                "用户申请退款，原因：" + text);

        // todo 消息推送

    }

    /**
     * 删除订单
     *
     * @param orderId 单号
     * @param uid     uid
     */
    @Override
    public void removeOrder(String orderId, Long uid) {
        AppStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (order == null) {
            throw exception(STORE_ORDER_NOT_EXISTS);
        }
        order = handleOrder(order);
        if (!OrderInfoEnum.STATUS_3.getValue().equals(order.getStatus())) {
            throw exception(ORDER_NOT_DELETE);
        }

        this.removeById(order.getId());

        // 增加状态
        storeOrderStatusService.create(uid, order.getId(),
                OrderLogEnum.REMOVE_ORDER.getValue(),
                OrderLogEnum.REMOVE_ORDER.getDesc());
    }

    /**
     * 未付款取消订单
     *
     * @param orderId 订单号
     * @param uid     用户id
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void cancelOrder(String orderId, Long uid) {
        log.info("订单取消：({})", orderId);
        AppStoreOrderQueryVo order = this.getOrderInfo(orderId, uid);
        if (ObjectUtil.isNull(order)) {
            throw exception(STORE_ORDER_NOT_EXISTS);
        }
        if (order.getPaid() != 0) {
            throw exception(ORDER_NOT_CANCEL);
        }

        this.regressionStock(order);

        this.regressionCoupon(order, 0);

        storeOrderMapper.deleteById(order.getId());
    }

    /**
     * 检查并添加自动出单任务
     *
     * @param orderInfo 订单信息
     */
    private void checkAndAddAutoOrderSend(AppStoreOrderQueryVo orderInfo) {
        try {
            // 获取店铺信息，检查是否开启自动出单
            StoreShopDO shop = appStoreShopService.getById(orderInfo.getShopId());
            if (shop == null || shop.getAutoOrderSend() == null || shop.getAutoOrderSend() != 1) {
                log.debug("店铺未开启自动出单功能，订单号：{}", orderInfo.getOrderId());
                return;
            }

            // 获取延迟时间，默认30秒
            Integer delaySeconds = shop.getAutoOrderSendDelay();
            if (delaySeconds == null || delaySeconds <= 0) {
                delaySeconds = (int) ShopConstants.AUTO_ORDER_SEND_DEFAULT_DELAY;
            }

            // 添加到自动出单延时队列
            RBlockingDeque<Object> blockingDeque = redissonClient
                    .getBlockingDeque(ShopConstants.REDIS_AUTO_ORDER_SEND_QUEUE);
            RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
            delayedQueue.offer(OrderMsg.builder().orderId(orderInfo.getOrderId()).build(),
                    delaySeconds, TimeUnit.SECONDS);

            log.info("订单已加入自动出单队列，订单号：{}，延迟时间：{}秒", orderInfo.getOrderId(), delaySeconds);

        } catch (Exception e) {
            log.error("添加自动出单队列失败，订单号：{}，错误信息：{}", orderInfo.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * 退回积分
     *
     * @param order 订单
     */
    private void regressionIntegral(AppStoreOrderQueryVo order, Integer type) {
        if (OrderInfoEnum.PAY_STATUS_1.getValue().equals(order.getPaid())
                || OrderStatusEnum.STATUS_MINUS_2.getValue().equals(order.getStatus())) {
            return;
        }

        if (order.getPayIntegral().compareTo(BigDecimal.ZERO) > 0) {
            order.setUseIntegral(order.getPayIntegral());
        }
        if (order.getUseIntegral().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        if (!OrderStatusEnum.STATUS_MINUS_2.getValue().equals(order.getStatus())
                && !OrderInfoEnum.REFUND_STATUS_2.getValue().equals(order.getRefundStatus())
                && order.getBackIntegral().compareTo(BigDecimal.ZERO) > 0) {
            return;
        }

        MemberUserDO yxUser = userService.getById(order.getUid());

        // 增加积分
        BigDecimal newIntegral = NumberUtil.add(order.getUseIntegral(), yxUser.getIntegral());
        yxUser.setIntegral(newIntegral);
        userService.updateById(yxUser);

        // 增加流水
        billService.income(yxUser.getId(), "积分回退", BillDetailEnum.CATEGORY_2.getValue(),
                BillDetailEnum.TYPE_8.getValue(),
                order.getUseIntegral().doubleValue(),
                newIntegral.doubleValue(),
                "购买商品失败,回退积分" + order.getUseIntegral(), order.getId().toString());

        // 更新回退积分
        StoreOrderDO storeOrder = new StoreOrderDO();
        storeOrder.setBackIntegral(order.getUseIntegral());
        storeOrder.setId(order.getId());
        this.updateById(storeOrder);
    }

    /**
     * 退回优惠券
     *
     * @param order 订单 todo
     */
    private void regressionCoupon(AppStoreOrderQueryVo order, Integer type) {
        if (OrderInfoEnum.PAY_STATUS_1.getValue().equals(order.getPaid())
                || OrderStatusEnum.STATUS_MINUS_2.getValue().equals(order.getStatus())) {
            return;
        }

        if (order.getCouponId() != null && order.getCouponId() > 0) {

            CouponUserDO couponUser = appCouponUserService
                    .getOne(Wrappers.<CouponUserDO>lambdaQuery()
                            .eq(CouponUserDO::getId, order.getCouponId())
                            .eq(CouponUserDO::getStatus, ShopCommonEnum.IS_STATUS_1.getValue())
                            .eq(CouponUserDO::getUserId, order.getUid()));

            if (ObjectUtil.isNotNull(couponUser)) {
                couponUser.setStatus(ShopCommonEnum.IS_STATUS_0.getValue());
                appCouponUserService.updateById(couponUser);
            }
        }
    }

    /**
     * 退回库存
     *
     * @param order 订单
     */
    private void regressionStock(AppStoreOrderQueryVo order) {
        if (OrderInfoEnum.PAY_STATUS_1.getValue().equals(order.getPaid())
                || OrderStatusEnum.STATUS_MINUS_2.getValue().equals(order.getStatus())) {
            return;
        }

        LambdaQueryWrapper<StoreOrderCartInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreOrderCartInfoDO::getOid, order.getId());

        List<StoreOrderCartInfoDO> cartInfoList = storeOrderCartInfoService.list(wrapper);
        for (StoreOrderCartInfoDO cartInfo : cartInfoList) {
            String newSku = StrUtil.replace(cartInfo.getSpec(), "|", ",");
            appStoreProductService.incProductStock(cartInfo.getNumber(), cartInfo.getProductId(), newSku, 0L, null);
        }
    }

    /**
     * 奖励积分
     *
     * @param order 订单
     */
    private void gainUserIntegral(AppStoreOrderQueryVo order) {
        if (order.getGainIntegral().compareTo(BigDecimal.ZERO) > 0) {
            MemberUserDO user = userService.getUser(order.getUid());

            BigDecimal newIntegral = NumberUtil.add(user.getIntegral(), order.getGainIntegral());
            user.setIntegral(newIntegral);
            user.setId(order.getUid());
            userService.updateById(user);

            // 增加流水
            billService.income(user.getId(), "购买商品赠送积分", BillDetailEnum.CATEGORY_2.getValue(),
                    BillDetailEnum.TYPE_9.getValue(),
                    order.getGainIntegral().doubleValue(),
                    newIntegral.doubleValue(),
                    "购买商品赠送" + order.getGainIntegral() + "积分", order.getId().toString());
        }
    }

    /**
     * 计算奖励的积分
     *
     * @param productIds
     * @return double
     */
    private BigDecimal getGainIntegral(List<String> productIds) {
        BigDecimal gainIntegral = BigDecimal.ZERO;
        for (int i = 0; i < productIds.size(); i++) {
            StoreProductDO storeProductDO = appStoreProductService.getById(productIds.get(i));
            if (storeProductDO.getGiveIntegral().intValue() == 0) {
                continue;
            }
            gainIntegral = NumberUtil.add(gainIntegral, storeProductDO.getGiveIntegral());
        }
        return gainIntegral;
    }

}
