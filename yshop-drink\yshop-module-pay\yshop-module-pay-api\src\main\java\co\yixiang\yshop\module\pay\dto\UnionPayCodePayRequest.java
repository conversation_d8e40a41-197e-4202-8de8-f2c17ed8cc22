package co.yixiang.yshop.module.pay.dto;

import lombok.Data;

/**
 * UnionPay付款码支付请求DTO
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
public class UnionPayCodePayRequest {
    
    /**
     * 商户号
     */
    private String merchantCode;
    
    /**
     * 终端号
     */
    private String terminalCode;
    
    /**
     * 交易金额（分）
     */
    private Long transactionAmount;
    
    /**
     * 交易币种
     */
    private String transactionCurrencyCode = "156";
    
    /**
     * 商户订单号
     */
    private String merchantOrderId;
    
    /**
     * 商户备注
     */
    private String merchantRemark;
    
    /**
     * 支付方式
     */
    private String payMode = "CODE_SCAN";
    
    /**
     * 付款码
     */
    private String payCode;
    
    /**
     * 设备类型
     */
    private String deviceType = "02";
    
    /**
     * 终端硬件序列号
     */
    private String serialNum;
    
    /**
     * 加密随机因子
     */
    private String encryptRandNum;
    
    /**
     * 商品信息
     */
    private String goods;
    
    /**
     * 商户兑余信息
     */
    private String srcReserved;
    
    /**
     * 门店号
     */
    private String storeId;
    
    /**
     * 操作员号
     */
    private String operatorId;
}
