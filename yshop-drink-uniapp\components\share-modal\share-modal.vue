<template>
  <view class="share-modal" v-if="visible" @click="handleMaskClick">
    <view class="modal-content" @click.stop>
      <view class="modal-header">
        <text class="modal-title">分享活动</text>
        <view class="close-btn" @click="close">
          <uni-icons type="close" size="20" color="#999"></uni-icons>
        </view>
      </view>

      <view class="share-preview">
        <image :src="shareInfo.shareImage" class="preview-image" mode="aspectFill"></image>
        <view class="preview-content">
          <text class="preview-title">{{ shareInfo.shareTitle }}</text>
          <text class="preview-desc">{{ shareInfo.shareDesc }}</text>
        </view>
      </view>

      <view class="share-options">
        <button class="option-item share-btn" open-type="share" @click="handleShareClick">
          <view class="option-icon wechat">
            <uni-icons type="redo" size="32" color="white"></uni-icons>
          </view>
          <text class="option-text">转发给好友</text>
          <text class="option-desc">直接转发小程序</text>
        </button>

        <view class="option-item share-btn" @click="generatePoster">
          <view class="option-icon poster">
            <uni-icons type="image" size="32" color="white"></uni-icons>
          </view>
          <text class="option-text">生成海报</text>
          <text class="option-desc">保存图片分享</text>
        </view>
      </view>

      <view class="share-tips">
        <uni-icons type="info" size="14" color="#999"></uni-icons>
        <text>转发给好友参与活动，完成邀请任务即可获得奖励</text>
      </view>

      <button class="cancel-btn" @click="close">取消</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ShareModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shareInfo: {
      type: Object,
      default: () => ({
        shareTitle: '',
        shareDesc: '',
        shareImage: '',
        miniProgramPath: ''
      })
    }
  },

  mounted() {
    // 设置分享信息到全局，供 onShareAppMessage 使用
    this.setGlobalShareInfo()
  },

  watch: {
    shareInfo: {
      handler() {
        this.setGlobalShareInfo()
      },
      deep: true
    }
  },

  methods: {
    // 设置全局分享信息
    setGlobalShareInfo() {
      // 将分享信息设置到全局，供父组件的 onShareAppMessage 使用
      if (typeof getApp === 'function') {
        const app = getApp()
        app.globalData = app.globalData || {}
        app.globalData.shareInfo = {
          title: this.shareInfo.shareTitle || '邀请您参与活动',
          path: this.shareInfo.miniProgramPath || '/pages/index/index',
          imageUrl: this.shareInfo.shareImage || ''
        }
      }
    },

    // 处理分享按钮点击
    handleShareClick() {
      // 设置分享信息
      this.setGlobalShareInfo()
      // 通知父组件准备分享
      this.$emit('share-prepare', {
        title: this.shareInfo.shareTitle || '邀请您参与活动',
        path: this.shareInfo.miniProgramPath || '/pages/index/index',
        imageUrl: this.shareInfo.shareImage || ''
      })
      // 延迟关闭弹窗，让分享操作先执行
      setTimeout(() => {
        this.$emit('share-success', { type: 'forward' })
        this.close()
      }, 100)
    },

    // 生成海报
    generatePoster() {
      this.$emit('generate-poster')
      this.close()
    },
    
    // 处理遮罩点击
    handleMaskClick() {
      this.close()
    },
    
    // 关闭弹窗
    close() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 80vh;
  padding: 40rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .modal-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.share-preview {
  display: flex;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
  
  .preview-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 20rpx;
  }
  
  .preview-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    
    .preview-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
    
    .preview-desc {
      font-size: 24rpx;
      color: #666;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }
}

.share-options {
  display: flex;
  gap: 40rpx;
  margin-bottom: 30rpx;

  .option-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 20rpx;
    background: #f8f9fa;
    border-radius: 20rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      border-color: #007aff;
    }
  }

  .share-btn {
    border: none;
    background: #f8f9fa;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    text-align: center;
    
    &::after {
      border: none;
    }

    .option-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15rpx;

      &.wechat {
        background: linear-gradient(135deg, #1aad19, #2dc653);
      }

      &.poster {
        background: linear-gradient(135deg, #667eea, #764ba2);
      }
    }

    .option-text {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      text-align: center;
      margin-bottom: 5rpx;
    }

    .option-desc {
      font-size: 22rpx;
      color: #999;
      text-align: center;
    }
  }
}

.share-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 30rpx;

  text {
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #666;
    line-height: 1.4;
  }
}



.cancel-btn {
  width: 100%;
  height: 80rpx;
  background: #f0f0f0;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  margin-top: 20rpx;
}
</style>
