package co.yixiang.yshop.module.pay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * UnionPay微信小程序支付配置
 * 仅包含环境相关配置，商户配置从数据库获取
 *
 * <AUTHOR>
 * @date 2025/7/14
 */
@Data
@Component
@ConfigurationProperties(prefix = "unionpay.wx.miniprogram")
public class UnionPayWxMiniProgramConfig {

    // ========== 环境相关配置（保留在YAML中） ==========

    /**
     * 银联网关地址（环境相关）
     */
    private String gatewayUrl;

    /**
     * AccessToken获取地址（环境相关）
     */
    private String accessTokenUrl;

    /**
     * 激活终端地址（环境相关）
     */
    private String activeTerminalUrl;

    /**
     * 付款码支付地址（环境相关）
     */
    private String codePayUrl;

    /**
     * 订单查询地址（环境相关）
     */
    private String queryUrl;

    // ========== 固定配置参数 ==========

    /**
     * 应用类型
     */
    private String appType = "02"; // 02：商户索引

    /**
     * 签名方法
     */
    private String signMethod = "RSA2";

    /**
     * 版本号
     */
    private String version = "1.0.0";
    
    /**
     * 编码格式
     */
    private String encoding = "UTF-8";
    
    /**
     * 交易类型
     */
    private String txnType = "01";
    
    /**
     * 交易子类
     */
    private String txnSubType = "01";
    
    /**
     * 业务类型
     */
    private String bizType = "000000";
    
    /**
     * 渠道类型
     */
    private String channelType = "08";
    
    /**
     * 接入类型
     */
    private String accessType = "0";
    
    /**
     * 货币代码
     */
    private String currencyCode = "156";
}
