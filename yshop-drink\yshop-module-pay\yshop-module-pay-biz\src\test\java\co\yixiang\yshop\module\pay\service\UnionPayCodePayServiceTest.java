package co.yixiang.yshop.module.pay.service;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

/**
 * UnionPay付款码支付服务测试
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@SpringBootTest
@ActiveProfiles("dev")
public class UnionPayCodePayServiceTest {
    
    @Resource
    private UnionPayCodePayService unionPayCodePayService;
    
    @Test
    public void testActiveTerminal() {
        try {
            String detailsId = "union_h51";
            
            System.out.println("开始测试银联终端激活...");
            System.out.println("商户配置ID: " + detailsId);
            
            Map<String, Object> result = unionPayCodePayService.activeTerminal(detailsId);
            
            System.out.println("激活结果: " + result);
            
            if (unionPayCodePayService.isActiveSuccess(result)) {
                System.out.println("终端激活成功！");
            } else {
                System.out.println("终端激活失败: " + result.get("errInfo"));
            }
            
        } catch (Exception e) {
            System.err.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testCodePay() {
        try {
            String orderId = "TEST" + System.currentTimeMillis();
            Long amount = 100L; // 1元，单位分
            String payCode = "134567890123456789"; // 测试付款码
            String detailsId = "union_miniapp_wx_1";
            
            System.out.println("开始测试银联付款码支付...");
            System.out.println("订单号: " + orderId);
            System.out.println("金额: " + amount + "分");
            System.out.println("付款码: " + payCode);
            System.out.println("商户配置ID: " + detailsId);
            
            Map<String, Object> result = unionPayCodePayService.codePay(orderId, amount, payCode, detailsId);
            
            System.out.println("支付结果: " + result);
            
            if (unionPayCodePayService.isPaySuccess(result)) {
                System.out.println("支付成功！");
            } else {
                System.out.println("支付失败: " + result.get("errInfo"));
            }
            
        } catch (Exception e) {
            System.err.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testQueryOrder() {
        try {
            String orderId = "TEST1722240000000"; // 使用一个已存在的订单号进行测试
            String detailsId = "union_miniapp_wx_1";
            
            System.out.println("开始测试银联订单查询...");
            System.out.println("订单号: " + orderId);
            System.out.println("商户配置ID: " + detailsId);
            
            Map<String, Object> result = unionPayCodePayService.queryOrder(orderId, detailsId);
            
            System.out.println("查询结果: " + result);
            
        } catch (Exception e) {
            System.err.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testIsActiveSuccess() {
        // 测试成功响应
        Map<String, Object> successResponse = Map.of(
            "errCode", "SUCCESS",
            "errInfo", ""
        );
        
        boolean isSuccess = unionPayCodePayService.isActiveSuccess(successResponse);
        System.out.println("激活成功响应测试: " + isSuccess); // 应该为true
        
        // 测试失败响应
        Map<String, Object> failResponse = Map.of(
            "errCode", "FAIL",
            "errInfo", "激活失败"
        );
        
        boolean isFail = unionPayCodePayService.isActiveSuccess(failResponse);
        System.out.println("激活失败响应测试: " + isFail); // 应该为false
        
        // 测试空响应
        boolean isNull = unionPayCodePayService.isActiveSuccess(null);
        System.out.println("空响应测试: " + isNull); // 应该为false
    }
    
    @Test
    public void testIsPaySuccess() {
        // 测试成功响应
        Map<String, Object> successResponse = Map.of(
            "errCode", "SUCCESS",
            "errInfo", ""
        );
        
        boolean isSuccess = unionPayCodePayService.isPaySuccess(successResponse);
        System.out.println("成功响应测试: " + isSuccess); // 应该为true
        
        // 测试失败响应
        Map<String, Object> failResponse = Map.of(
            "errCode", "FAIL",
            "errInfo", "支付失败"
        );
        
        boolean isFail = unionPayCodePayService.isPaySuccess(failResponse);
        System.out.println("失败响应测试: " + isFail); // 应该为false
        
        // 测试空响应
        boolean isNull = unionPayCodePayService.isPaySuccess(null);
        System.out.println("空响应测试: " + isNull); // 应该为false
    }
}
