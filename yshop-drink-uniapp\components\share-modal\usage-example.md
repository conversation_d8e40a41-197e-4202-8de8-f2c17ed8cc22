# ShareModal 组件使用说明

## 基本用法

在父组件中使用分享组件，需要同时配置 `onShareAppMessage` 生命周期函数：

```vue
<template>
  <view>
    <!-- 其他内容 -->
    
    <!-- 分享弹窗 -->
    <share-modal 
      :visible="shareModalVisible" 
      :shareInfo="shareInfo"
      @close="shareModalVisible = false"
      @share-prepare="handleSharePrepare"
      @share-success="handleShareSuccess"
      @generate-poster="handleGeneratePoster"
    />
  </view>
</template>

<script>
import ShareModal from '@/components/share-modal/share-modal.vue'

export default {
  components: {
    ShareModal
  },
  
  data() {
    return {
      shareModalVisible: false,
      shareInfo: {
        shareTitle: '精彩活动等你来参与',
        shareDesc: '快来参与这个精彩的活动，赢取丰厚奖励！',
        shareImage: 'https://example.com/share-image.jpg',
        miniProgramPath: '/pages/activity/detail?id=123'
      },
      currentShareInfo: null // 存储当前分享信息
    }
  },
  
  // 重要：必须定义 onShareAppMessage 生命周期函数
  onShareAppMessage(res) {
    // 使用准备好的分享信息
    if (this.currentShareInfo) {
      return this.currentShareInfo
    }
    
    // 默认分享信息
    return {
      title: this.shareInfo.shareTitle || '精彩活动等你来参与',
      path: this.shareInfo.miniProgramPath || '/pages/index/index',
      imageUrl: this.shareInfo.shareImage || ''
    }
  },
  
  methods: {
    // 显示分享弹窗
    showShareModal() {
      this.shareModalVisible = true
    },
    
    // 处理分享准备
    handleSharePrepare(shareData) {
      console.log('准备分享:', shareData)
      // 存储分享信息供 onShareAppMessage 使用
      this.currentShareInfo = shareData
    },
    
    // 处理分享成功
    handleShareSuccess(result) {
      console.log('分享成功:', result)
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
      // 可以在这里调用分享统计接口
    },
    
    // 处理生成海报
    handleGeneratePoster() {
      console.log('生成海报')
      // 实现海报生成逻辑
    }
  }
}
</script>
```

## 配置参数说明

### shareInfo 对象属性

- `shareTitle`: 分享标题
- `shareDesc`: 分享描述
- `shareImage`: 分享图片链接
- `miniProgramPath`: 小程序页面路径（包含参数）

### 事件说明

- `@close`: 关闭弹窗
- `@share-prepare`: 分享准备完成，会传入分享数据
- `@share-success`: 分享成功
- `@generate-poster`: 生成海报

## 重要注意事项

1. **必须定义 onShareAppMessage**: 父组件必须定义 `onShareAppMessage` 生命周期函数，否则分享功能无法正常工作。

2. **分享信息传递**: 通过 `@share-prepare` 事件接收分享数据，然后在 `onShareAppMessage` 中返回。

3. **小程序限制**: 微信小程序不支持主动调用分享，只能通过 `<button open-type="share">` 触发。

4. **路径参数**: `miniProgramPath` 可以包含查询参数，用于传递活动ID等信息。

## 常见问题

### Q: 分享后如何获取分享结果？
A: 微信小程序的分享是异步的，无法直接获取分享结果，只能通过 `@share-success` 事件知道用户点击了分享按钮。

### Q: 如何自定义分享内容？
A: 通过修改 `shareInfo` 对象的属性来自定义分享标题、描述、图片和跳转路径。

### Q: 分享按钮点击没反应？
A: 检查父组件是否正确定义了 `onShareAppMessage` 函数，这是分享功能正常工作的前提。