# 银联付款码支付集成文档

## 概述

本文档介绍银联付款码支付功能的集成实现，该功能允许用户通过扫码枪扫描银联付款码完成支付。

## 功能特性

- 支持银联付款码扫码支付
- 集成现有的扫码枪输入处理逻辑
- 支持多商户配置
- 完整的错误处理和日志记录
- 支付结果验证和订单查询

## 技术架构

### 1. 服务层架构

```
UnionPayCodePayService (接口)
└── UnionPayCodePayServiceImpl (实现)
    ├── UnionPayConfigService (配置服务)
    ├── UnionPayAccessTokenService (令牌服务)
    └── UnionPayWxMiniProgramConfig (配置类)
```

### 2. 核心组件

#### UnionPayCodePayService
- `activeTerminal()` - 激活终端
- `codePay()` - 执行付款码支付
- `queryOrder()` - 查询订单状态
- `isPaySuccess()` - 验证支付结果
- `isActiveSuccess()` - 验证终端激活结果

#### UnionPayCodePayRequest
付款码支付请求参数DTO，包含：
- merchantCode - 商户号
- terminalCode - 终端号
- transactionAmount - 交易金额（分）
- payCode - 付款码
- 其他必要参数

## 配置说明

### 1. YAML配置

```yaml
unionpay:
  wx:
    miniprogram:
      gateway-url: https://api-mop.chinaums.com/v1/netpay/wx/unified-order
      access-token-url: https://api-mop.chinaums.com/v2/token/access
      active-terminal-url: https://api-mop.chinaums.com/v2/poslink/transaction/activeterminal
      code-pay-url: https://api-mop.chinaums.com/v4/poslink/transaction/pay
      query-url: https://api-mop.chinaums.com/v2/poslink/transaction/query
```

### 2. 数据库配置

商户配置存储在 `merchant_details` 表中：
- details_id: `union_miniapp_wx_{tenantId}`
- pay_type: `union`
- 其他银联商户参数

## API接口

### 1. 激活终端API

**请求地址**: `https://api-mop.chinaums.com/v2/poslink/transaction/activeterminal`

**请求方法**: POST

**请求头**:
```
Content-Type: application/json
Authorization: OPEN-ACCESS-TOKEN AccessToken={accessToken},AppId={appId}
```

**请求参数**:
```json
{
  "merchantCode": "商户号",
  "terminalCode": "终端号"
}
```

**响应示例**:
```json
{
  "errCode": "SUCCESS",
  "errInfo": ""
}
```

### 2. 付款码支付API

**请求地址**: `https://api-mop.chinaums.com/v4/poslink/transaction/pay`

**请求方法**: POST

**请求头**:
```
Content-Type: application/json
Authorization: {accessToken}
```

**请求参数**:
```json
{
  "merchantCode": "商户号",
  "terminalCode": "终端号", 
  "transactionAmount": 100,
  "transactionCurrencyCode": "156",
  "merchantOrderId": "商户订单号",
  "payMode": "CODE_SCAN",
  "payCode": "付款码",
  "deviceType": "02"
}
```

**响应示例**:
```json
{
  "errCode": "SUCCESS",
  "errInfo": "",
  "transactionId": "银联交易号",
  "merchantOrderId": "商户订单号"
}
```

### 3. 订单查询API

**请求地址**: `https://api-mop.chinaums.com/v2/poslink/transaction/query`

**请求参数**:
```json
{
  "merchantCode": "商户号",
  "terminalCode": "终端号",
  "merchantOrderId": "商户订单号"
}
```

## 集成流程

### 1. 支付流程

```mermaid
sequenceDiagram
    participant Frontend as 前端收银台
    participant Backend as 后端服务
    participant UnionPay as 银联API

    Frontend->>Backend: 创建订单
    Backend-->>Frontend: 返回订单ID
    Frontend->>Frontend: 等待扫码枪输入
    Frontend->>Backend: 发送付款码支付请求
    Backend->>UnionPay: 激活终端API
    UnionPay-->>Backend: 返回激活结果
    Backend->>UnionPay: 调用付款码支付API
    UnionPay-->>Backend: 返回支付结果
    Backend->>Backend: 处理支付成功逻辑
    Backend-->>Frontend: 返回支付结果
    Frontend->>Frontend: 显示支付结果
```

### 2. 前端集成

前端已支持银联付款码支付：

```javascript
// 支付类型选择
selectPayType('union')

// 扫码枪输入处理
if(orderId.value && payType.value == 'union'){
    doPay(code) // code为扫码枪获取的付款码
}

// 支付请求
CashierApi.pay({
    uni: orderId.value,
    from: 'scan',
    paytype: 'union',
    authCode: code
})
```

### 3. 后端集成

在 `AppStoreOrderServiceImpl` 中的银联支付处理：

```java
case UNION:
    if (AppFromEnum.SCAN_PC.getValue().equals(param.getFrom())) {
        // 银联付款码支付（包含自动激活终端）
        detailsId = "union_miniapp_wx_" + tenantId;
        Map<String, Object> mapRes = unionPayCodePayService.codePay(
                param.getUni(),
                price.multiply(new BigDecimal("100")).longValue(),
                param.getAuthCode(),
                detailsId
        );

        if (unionPayCodePayService.isPaySuccess(mapRes)) {
            paySuccess(param.getUni(), param.getPaytype());
        } else {
            throw exception(new ErrorCode(20247172, "银联付款码支付失败"));
        }
    }
    break;
```

## 错误处理

### 1. 常见错误码

- `SUCCESS` - 支付成功
- `FAIL` - 支付失败
- `ERROR` - 系统错误
- 网络异常 - 连接超时等

### 2. 异常处理策略

- 网络异常：重试机制
- 参数错误：参数验证
- 业务异常：错误信息展示
- 系统异常：日志记录和告警

## 测试说明

### 1. 单元测试

运行 `UnionPayCodePayServiceTest` 进行功能测试：

```bash
mvn test -Dtest=UnionPayCodePayServiceTest
```

### 2. 集成测试

1. 配置测试环境的银联商户信息
2. 使用测试付款码进行支付测试
3. 验证支付结果和订单状态

### 3. 测试用例

- 终端激活功能测试
- 正常支付流程测试
- 付款码格式验证
- 金额计算验证
- 网络异常处理测试
- 支付结果验证测试
- 激活结果验证测试

## 部署说明

### 1. 环境配置

确保以下配置正确：
- 银联API地址（测试/生产环境）
- 商户配置信息
- AccessToken缓存配置

### 2. 监控指标

- 支付成功率
- 响应时间
- 错误率统计
- AccessToken刷新频率

## 注意事项

1. **终端激活**: 首次使用前需要激活终端，系统会自动处理
2. **金额单位**: 后端处理时需要将元转换为分
3. **付款码格式**: 银联付款码通常为18位数字
4. **超时处理**: 付款码支付有时效性，需要及时处理
5. **重复支付**: 避免同一订单重复支付
6. **日志记录**: 记录关键操作日志便于问题排查
7. **API版本**: 注意不同API使用不同版本号（v2/v4）

## 相关文档

- [银联支付API文档](http://yqhpyp.oss-cn-shanghai.aliyuncs.com/20250714/dc34adc3b5484caa8a24b1a09331cb09.pdf)
- [银联授权文档](http://yqhpyp.oss-cn-shanghai.aliyuncs.com/20250714/25f27c22d8d946f09c2fc3de477b2e28.pdf)
- [UnionPay多商户实现文档](./UnionPay-Multi-Merchant-Implementation.md)
- [UnionPay数据库迁移指南](./UnionPay-Database-Migration-Guide.md)
